--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset vladimir.minakov:2025-01-29-SWS-51271-add-default-lobby-domain-field
--comment Add defaultLobbyDomain field to entities table for fallback when staticDomainPool is not configured
SET search_path = swmanagement;

ALTER TABLE entities ADD COLUMN default_lobby_domain_id INTEGER;
COMMENT ON COLUMN entities.default_lobby_domain_id IS 'Default lobby domain ID to use when entity has no static domain pool configured for lobby type';

RESET search_path;
--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entities DROP COLUMN default_lobby_domain_id;
--rollback RESET search_path;
