import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { LobbyDomainAttributes } from "../entities/lobbyDomain";
import { DomainStatus } from "../entities/domain";

export interface LobbyDomainDBInstance extends Model<
        InferAttributes<LobbyDomainDBInstance>,
        InferCreationAttributes<LobbyDomainDBInstance>
    >,
    LobbyDomainAttributes {
}

type ILobbyDomainModel = ModelStatic<LobbyDomainDBInstance>;
const LobbyDomainModel: ILobbyDomainModel = db.define<LobbyDomainDBInstance, LobbyDomainAttributes>(
    "LobbyDomainModel",
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            field: "id"
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            field: "name"
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            field: "description"
        },
        provider: {
            type: DataTypes.STRING,
            allowNull: true,
            field: "provider"
        },
        status: {
            type: DataTypes.ENUM(...Object.values(DomainStatus)),
            allowNull: false,
            field: "status"
        },
        expiryDate: {
            type: DataTypes.DATE,
            allowNull: true,
            field: "expiry_date"
        },
        isActive: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            field: "is_active"
        },
        createdAt: {
            type: DataTypes.DATE,
            field: "created_at",
        },
        updatedAt: {
            type: DataTypes.DATE,
            field: "updated_at",
        },
    },
    {
        tableName: "lobby_domains",
        underscored: true
    }
);

export const getLobbyDomainModel = () => LobbyDomainModel;
