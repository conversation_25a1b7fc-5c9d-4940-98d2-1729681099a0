import * as express from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getEntity,
    getEntityPathFromQuery,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import { getDynamicDomainService, getStaticDomainService } from "../services/domain";
import { getEntityStaticDomainService } from "../services/entityStaticDomainService";
import { getEntityDynamicDomainService } from "../services/entityDynamicDomainService";
import { BaseEntity, ChildEntity } from "../entities/entity";
import EntitySettingsService from "../services/settings";
import { getLobbyDomainService } from "../services/lobbyDomain";
import { DomainStatus, StaticDomainType } from "../entities/domain";

const router: express.Router = express.Router();

async function getDynamicDomains(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domains = await getDynamicDomainService().findAll();
        res.send(domains);
        next();
    } catch (err) {
        next(err);
    }
}

async function createDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getDynamicDomainService().create(req.body);
        res.status(201).send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function getDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getDynamicDomainService().findOne(req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const update = req.body;
        update.id = req.params.id;
        const domain = await getDynamicDomainService().update(update);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function removeDynamicDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getDynamicDomainService().remove(req.params.id);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function getStaticDomains(req: express.Request & KeyEntityHolder,
                                res: express.Response,
                                next: express.NextFunction) {
    try {
        let entity: BaseEntity;
        if (req.query.path) {
            entity = req.keyEntity.find({ path: getEntityPathFromQuery(req) });
        }

        let domains = await getStaticDomainService().findAll({ entity });
        if (entity && !entity.isMaster()) {
            const parentSettings = await new EntitySettingsService((entity as ChildEntity).getParent()).get();
            if (parentSettings.allowedStaticDomainsForChildId) {
                domains = domains.filter(d => parentSettings.allowedStaticDomainsForChildId.indexOf(d.id) !== -1);
            }
        }
        res.send(domains);
        next();
    } catch (err) {
        next(err);
    }
}

async function createStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainService().create(req.body);
        res.status(201).send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function getStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const domain = await getStaticDomainService().findOne(req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function updateStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const update = req.body;
        update.id = req.params.id;
        const domain = await getStaticDomainService().update(update);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function removeStaticDomain(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getStaticDomainService().remove(req.params.id);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

async function getDynamicEntityDomain(req: express.Request & KeyEntityHolder,
                                      res: express.Response,
                                      next: express.NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const domain = await getEntityDynamicDomainService().get(entity);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function setDynamicEntityDomain(req: express.Request & KeyEntityHolder,
                                      res: express.Response,
                                      next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domain = await getEntityDynamicDomainService().set(entity, req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function resetDynamicEntityDomain(req: express.Request & KeyEntityHolder,
                                        res: express.Response,
                                        next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domain = await getEntityDynamicDomainService().reset(entity);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function getStaticEntityDomain(req: express.Request & KeyEntityHolder,
                                     res: express.Response,
                                     next: express.NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const domain = await getEntityStaticDomainService().get(entity);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function setStaticEntityDomain(req: express.Request & KeyEntityHolder,
                                     res: express.Response,
                                     next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domain = await getEntityStaticDomainService().set(entity, req.params.id);
        res.send(domain);
        next();
    } catch (err) {
        next(err);
    }
}

async function resetStaticEntityDomain(req: express.Request & KeyEntityHolder,
                                       res: express.Response,
                                       next: express.NextFunction) {
    try {
        const entity = getEntity(req);
        const domain = await getEntityStaticDomainService().reset(entity);
        if (!domain) {
            res.status(204).end();
        } else {
            res.send(domain);
        }
        next();
    } catch (err) {
        next(err);
    }
}

const setStaticDomainTagsHandler = async (req: express.Request & KeyEntityHolder,
                                          res: express.Response,
                                          next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const service = getEntityStaticDomainService();
        const result = await service.setTags(entity, req.body.tags);
        res.send(result);
    } catch (err) {
        next(err);
    }
};

const resetStaticDomainTagsHandler = async (req: express.Request & KeyEntityHolder,
                                            res: express.Response,
                                            next: express.NextFunction) => {
    try {
        const entity = getEntity(req);
        const service = getEntityStaticDomainService();
        const result = await service.resetTags(entity);
        res.send(result);
    } catch (err) {
        next(err);
    }
};

// dynamic domain management
router.get("/domains/dynamic", authenticate, authorize, getDynamicDomains);
router.post("/domains/dynamic", authenticate, authorize,
    validate({
        domain: { notEmpty: true, isDomain: true },
        environment: { notEmpty: true, isWord: true },
        description: { optional: true, isString: true },
        provider: { optional: true, isString: true },
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        expiryDate: { optional: true, isISO8601: true }
    }),
    auditable,
    createDynamicDomain);
router.get("/domains/dynamic/:id", authenticate, authorize, decodePid(), getDynamicDomain);
router.patch("/domains/dynamic/:id", authenticate, authorize,
    validate({
        domain: { optional: true, isDomain: true },
        environment: { optional: true, isWord: true },
        description: { optional: true, isString: true },
        provider: { optional: true, isString: true },
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        expiryDate: { optional: true, isISO8601: true }
    }),
    auditable,
    decodePid(),
    updateDynamicDomain);
router.delete("/domains/dynamic/:id", authenticate, authorize, decodePid(), auditable, removeDynamicDomain);

// static domain management
router.get("/domains/static", authenticate, authorize, getStaticDomains);
router.post("/domains/static", authenticate, authorize,
    validate({
        domain: { notEmpty: true, isDomain: true },
        description: { optional: true, isString: true },
        provider: { optional: true, isString: true },
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        type: { optional: true, isIn: { options: [[StaticDomainType.STATIC, StaticDomainType.LOBBY, StaticDomainType.LIVE_STREAMING, StaticDomainType.EHUB]] } },
        expiryDate: { optional: true, isISO8601: true }
    }),
    auditable,
    createStaticDomain);
router.get("/domains/static/:id", authenticate, authorize, decodePid(), getStaticDomain);
router.patch("/domains/static/:id", authenticate, authorize,
    validate({
        domain: { optional: true, isDomain: true },
        description: { optional: true, isString: true },
        provider: { optional: true, isString: true },
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        type: { optional: true, isIn: { options: [["Static", "Lobby", "Live Streaming", "Ehub"]] } },
        expiryDate: { optional: true, isISO8601: true }
    }),
    decodePid(),
    auditable,
    updateStaticDomain);
router.delete("/domains/static/:id", authenticate, authorize, decodePid(), auditable, removeStaticDomain);

router.get("/domains/lobby",
    authenticate,
    authorize,
    async function (req, res, next) {
        try {
            const domains = await getLobbyDomainService().findAll();
            res.send(domains);
            next();
        } catch (err) {
            next(err);
        }
    });

router.post("/domains/lobby",
    authenticate,
    authorize,
    validate({
        name: { notEmpty: true, isLobbyDomainTemplate: true },
        description: { optional: true, isString: true },
        provider: { optional: true, isString: true },
        status: { optional: true, isIn: { options: [[DomainStatus.ACTIVE, DomainStatus.SUSPENDED]] } },
        expiryDate: { optional: true, isISO8601: true },
        isActive: { optional: true, isBoolean: true },
    }),
    auditable,
    async function (req, res, next) {
        try {
            const domain = await getLobbyDomainService().create(req.body);
            res.status(201).send(domain);
            next();
        } catch (err) {
            next(err);
        }
    });

router.get("/domains/lobby/:id",
    authenticate,
    authorize,
    decodePid(),
    async function (req, res, next) {
        try {
            const domain = await getLobbyDomainService().findOne(req.params.id);
            res.send(domain);
            next();
        } catch (err) {
            next(err);
        }
    });

router.patch("/domains/lobby/:id",
    authenticate,
    authorize,
    validate({
        name: { optional: true, isLobbyDomainTemplate: true },
        isActive: { optional: true, isBoolean: true },
    }),
    decodePid(),
    auditable,
    async function (req, res, next) {
        try {
            const domain = await getLobbyDomainService().update(req.params.id, req.body);
            res.send(domain);
            next();
        } catch (err) {
            next(err);
        }
    });

router.delete("/domains/lobby/:id",
    authenticate,
    authorize,
    decodePid(),
    auditable,
    async function (req, res, next) {
        try {
            await getLobbyDomainService().remove(req.params.id);
            res.status(204).end();
            next();
        } catch (err) {
            next(err);
        }
    });

// entity dynamic domain
router.put("/:path/entitydomain/dynamic/:id", authenticate, authorize, decodePid(), auditable, setDynamicEntityDomain);
router.get("/:path/entitydomain/dynamic", authenticate, authorize, decodePid(), getDynamicEntityDomain);
router.delete("/:path/entitydomain/dynamic", authenticate, authorize, decodePid(), auditable, resetDynamicEntityDomain);
router.put("/entitydomain/dynamic/:id", authenticate, authorize, decodePid(), auditable, setDynamicEntityDomain);
router.get("/entitydomain/dynamic", authenticate, authorize, decodePid(), getDynamicEntityDomain);
router.delete("/entitydomain/dynamic", authenticate, authorize, decodePid(), auditable, resetDynamicEntityDomain);

// entity static domain
router.put("/:path/entitydomain/static/tags",
    authenticate,
    authorize,
    validate({
        tags: {
            notEmpty: true,
            isDomainArray: { errorMessage: "should be an array of domains" }
        }
    }),
    auditable,
    setStaticDomainTagsHandler);

router.delete("/:path/entitydomain/static/tags",
    authenticate,
    authorize,
    auditable,
    resetStaticDomainTagsHandler);

router.put("/:path/entitydomain/static/:id", authenticate, authorize, decodePid(), auditable, setStaticEntityDomain);
router.get("/:path/entitydomain/static", authenticate, authorize, decodePid(), getStaticEntityDomain);
router.delete("/:path/entitydomain/static", authenticate, authorize, decodePid(), auditable, resetStaticEntityDomain);
router.put("/entitydomain/static/:id", authenticate, authorize, decodePid(), auditable, setStaticEntityDomain);
router.get("/entitydomain/static", authenticate, authorize, decodePid(), getStaticEntityDomain);
router.delete("/entitydomain/static", authenticate, authorize, decodePid(), auditable, resetStaticEntityDomain);

export default router;
