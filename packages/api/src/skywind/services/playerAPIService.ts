import { lazy, logging } from "@skywind-group/sw-utils";
import { Player, PlayerInfoWithBalance } from "../entities/player";
import * as Errors from "../errors";
import * as SecurityService from "./security";
import * as PaymentService from "./payment";
import { PaymentMethodInfo } from "../entities/payment_method";
import { OrderInfo } from "../entities/payment";
import { LobbyGamesParams } from "../entities/game";
import { BrandEntity } from "../entities/brand";
import { parseFilter } from "./filter";
import { BaseEntity, ENTITY_TYPE } from "../entities/entity";
import { generatePlayerLoginToken, PlayerLoginTokenData } from "../utils/token";
import { CurrencyFormatConfig, EntitySettings } from "../entities/settings";
import EntitySettingsService, * as Settings from "./settings";
import { getEntitySettings } from "./settings";
import EntityCache from "../cache/entity";
import { LoginInfo } from "./playerLogin";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { getPlayerInfoService } from "./playerInfo";
import { getBrandPlayerService } from "./brandPlayer";
import { getLobbyEntityGames } from "./lobby";
import { omit } from "lodash";
import * as LobbyCache from "../cache/lobby";
import { getPlayerGameActivities } from "./game";
import { setPlayerGameActivities, updateMenuItems } from "./lobby/getMenuItemGames";
import { getEntityStaticDomainService } from "./entityStaticDomainService";
import { buildDynamicLiveManagerUrl } from "./entityDynamicDomainService";
import { LiveManagerService } from "./live";
import { Models } from "../models/models";
import { getMerchantService } from "./merchant";
import { UrlPlaceholders } from "./gameUrl/urlPlaceholders";
import { JwtPayload } from "./playerSecurity";

const PlayerModel = Models.PlayerModel;
const PLACEHOLDER_REGEXP = /{.*?}/;

export interface LogoutData {
    brandId: number;
    code: string;
    sessionId: string;
    gameGroup?: string;
    gameGroupId?: number;
}

export interface UpdateData {
    email?: string;
    firstName?: string;
    lastName?: string;
    nickname?: string;
    customData?: any;
}

export interface SetPasswordData {
    password: string;
    newPassword: string;
}

export interface PlayerRequestData extends PlayerLoginTokenData {
    brand: BaseEntity;
    tokenData: PlayerLoginTokenData;
    tokenPayload?: JwtPayload;
}

type ExtendedPlayerInfo = PlayerInfoWithBalance & {
    historyUrl?: string;
    currencyFormatConfig?: CurrencyFormatConfig
};

export async function getHistoryUrl(entity: BaseEntity, settings: EntitySettings, log: logging.Logger) {
    if (!settings.supportHistoryUrl) {
        return;
    }
    let historyUrl = settings.urlParams?.history_url;
    if (!historyUrl) {
        return;
    }
    const staticDomain = await getEntityStaticDomainService().get(entity);
    if (historyUrl.match(UrlPlaceholders.STATIC_DOMAIN) && !staticDomain) {
        const error = new Errors.StaticDomainNotDefined();
        log.error(error, "Static domain not defined");
        return;
    }
    if (historyUrl && historyUrl.toString().match(PLACEHOLDER_REGEXP)) {
        historyUrl = historyUrl.replace(UrlPlaceholders.STATIC_DOMAIN, staticDomain.domain);
    }
    return decodeURI(historyUrl);
}

export function isMerchantType(req: PlayerRequestData): boolean {
    return req.brand.type === ENTITY_TYPE.MERCHANT &&
        (Boolean(req.customerSessionId) || Boolean(req.isExternalTerminal));
}

export async function merchantPlayerInfo(req: PlayerRequestData): Promise<PlayerInfoWithBalance> {
    return await getMerchantService().getPlayerWithBalance({
        code: req.playerCode,
        isTest: req.test,
        brandId: req.brandId,
        language: req.language,
        country: req.country,
        gameGroup: req.gameGroup,
        currency: req.currency,
        ipmSessionId: req.customerSessionId,
        ...req.tokenData
    });
}

export async function toPlayerInfo(req: PlayerRequestData, log: logging.Logger): Promise<ExtendedPlayerInfo> {
    let playerInfo: ExtendedPlayerInfo;
    if (isMerchantType(req)) {
        playerInfo = await merchantPlayerInfo(req);
    } else {
        playerInfo = await getPlayerAPIService().getPlayerInfo(req.brandId, req.playerCode);
    }
    const settings = await Settings.getEntitySettings(req.brand.path);
    playerInfo.historyUrl = await getHistoryUrl(req.brand, settings, log);
    if (playerInfo.currency && settings.currencyFormatSettings) {
        playerInfo.currencyFormatConfig = settings.currencyFormatSettings[playerInfo.currency];
    }
    return playerInfo;
}

/**
 * Main goal - to serve requests coming to Player API, which is mostly used by Terminals, Lobbies and Sites.
 */
export class PlayerAPIService {

    public async logoutPlayer({ brandId, code, sessionId }: LogoutData): Promise<void> {
        const player = await PlayerAPIService.getPlayerAndCheckSession(sessionId, code, brandId);

        await createPlayerSessionFacade().kill({
            brandId: player.brandId,
            playerCode: player.code,
            reason: "Logout Player"
        });
    }

    public async refreshPlayerToken(req: PlayerLoginTokenData): Promise<LoginInfo> {
        const player = await PlayerAPIService.getPlayerAndCheckSession(req.sessionId, req.playerCode, req.brandId);
        const token: string = await generatePlayerLoginToken({
            playerCode: player.code,
            brandId: player.brandId,
            gameGroupId: player.gamegroupId,
            gameGroup: player.gamegroupName,
            currency: player.currency,
            test: player.isTest,
            ...(omit(req.tokenData, ["iat", "exp", "iss"]))
        });

        return {
            code: player.code,
            token,
            isPasswordTemp: !!player.isPasswordTemp,
        };
    }

    public async getPaymentMethods(brandId: number, type: string): Promise<PaymentMethodInfo[]> {
        const entity = await EntityCache.findOne({ id: brandId });
        return PaymentService.find(entity as BrandEntity, { type: type, status: "normal" });
    }

    public async getPlayerPayments(brandId: number, query: Record<string, any>): Promise<OrderInfo[]> {
        const entity = await EntityCache.findOne({ id: brandId });
        return PaymentService.getPaymentsList(entity as BrandEntity,
            parseFilter(query, PaymentService.queryParamsKeys));
    }

    public async getPlayerInfo(brandId: number, playerCode: string): Promise<PlayerInfoWithBalance> {
        const player: Player = await getBrandPlayerService().getPlayer(brandId, playerCode);
        return player.toInfoWithBalances();
    }

    public async updatePlayerInfo(entity: BaseEntity, playerCode: string, data: UpdateData): Promise<PlayerInfoWithBalance> {
        const updateData: UpdateData = {};
        const entitySettingsService = new EntitySettingsService(entity);
        const entitySettings: EntitySettings = await entitySettingsService.get();
        const isNotOnlyNicknameUpdate = Object.keys(data).some(key => key !== "nickname");
        if (!entitySettings.storePlayerInfo && isNotOnlyNicknameUpdate) {
            return Promise.reject(new Errors.PlayerInfoCannotBeSaved());
        }
        if (data.customData instanceof Object) {
            updateData.customData = data.customData;
        }
        if (typeof data.firstName === "string") {
            updateData.firstName = data.firstName;
        }
        if (typeof data.lastName === "string") {
            updateData.lastName = data.lastName;
        }
        if (data.email) {
            updateData.email = data.email;
        }
        const updatePlayerResult = await PlayerModel.update(
            updateData,
            {
                where: {
                    brandId: entity.id,
                    code: playerCode,
                },
            }
        );
        if (data.nickname) {
            await getPlayerInfoService().createOrUpdate({
                nickname: data.nickname,
                brandId: entity.id,
                playerCode,
                isMerchantPlayer: false
            }, { increaseNicknameChangeAttempts: true });
        }
        if (!updatePlayerResult[0] && !data.nickname) {
            return Promise.reject(new Errors.PlayerInfoHasNotChanged());
        }

        return (await getBrandPlayerService().getPlayer(entity.id, playerCode)).toInfoWithBalances();
    }

    public async suspendPlayer(brandId: number, playerCode: string): Promise<void> {
        const updatePlayerResult = await PlayerModel.update(
            {
                status: "suspended",
            },
            {
                where: {
                    brandId: brandId,
                    code: playerCode,
                },
            }
        );
        if (!updatePlayerResult[0]) {
            return Promise.reject(new Errors.PlayerInfoHasNotChanged());
        }
    }

    public async setNewPassword(brand: BaseEntity,
                                playerCode: string,
                                passwords: SetPasswordData): Promise<PlayerInfoWithBalance> {
        const player: Player = await getBrandPlayerService().getPlayer(brand.id, playerCode);

        const settings: EntitySettings = await getEntitySettings(brand.path);
        const passwordIsValid = await getBrandPlayerService().validatePlayerPassword(settings, passwords.newPassword);
        await getBrandPlayerService().validationPasswordError(passwordIsValid);

        if (!player.password) {
            return Promise.reject(new Errors.PlayerCreatedWithoutPassword());
        }

        if (passwords.password === passwords.newPassword) {
            return Promise.reject(new Errors.PasswordNotUnique());
        }

        if (await SecurityService.encryptPassword(player.salt, passwords.password) !== player.password) {
            return Promise.reject(new Errors.PasswordDoesntMatch());
        }

        const updatePasswordResult = await getBrandPlayerService().changePlayerPassword(
            brand,
            passwords.newPassword,
            { isPasswordTemp: false },
            { code: playerCode }
        );

        if (!updatePasswordResult) {
            return Promise.reject(new Errors.PasswordHasNotChanged());
        }

        return player.toInfoWithBalances();
    }

    public async getLobby(entity: BaseEntity, lobbyId: number, playerCode: string, params: LobbyGamesParams) {
        const item = await LobbyCache.findOne(entity, lobbyId, params);
        const lobby = structuredClone(item);

        const { social, useSocialCasinoOperator } = await getEntitySettings(entity.path);
        const liveManagerUrl = await buildDynamicLiveManagerUrl(entity, params.socketVersion);
        const menuItems = lobby.info?.menuItems;
        if (params.includeGamesLimitRanges && Array.isArray(menuItems) && menuItems.length) {
            const items = await getPlayerGameActivities(entity, playerCode);
            if (items.length) {
                setPlayerGameActivities(menuItems, items);
            }
            const { url, path } = liveManagerUrl ?? {};
            if (url) {
                const baseUrl = path ? `${url}/${path}` : url;
                const tables = await LiveManagerService.getLiveTables(baseUrl);
                if (tables.length) {
                    updateMenuItems(menuItems, (game) => {
                        if (game.features?.live) {
                            const { provider, tableId } = game.features.live;
                            const liveGameInfo = tables
                                .filter(table => provider === table.provider)
                                .find(table => tableId === table.id);
                            if (liveGameInfo) {
                                game.features.live.gameType = liveGameInfo.type;
                                game.features.live.dealer = liveGameInfo.dealer;
                                game.features.live.status = liveGameInfo.status;
                                game.features.live.language = liveGameInfo.language;
                            }
                        }
                        return game;
                    });
                }
            }
        }
        lobby.info = {
            ...lobby.info ?? {},
            ...(social !== undefined ? { social } : {}),
            ...(useSocialCasinoOperator !== undefined ? { useSocialCasinoOperator } : {}),
            liveManagerUrl,
        }

        return lobby;
    }

    public async getGames(
        entity: BaseEntity,
        lobbyId: number,
        params?: LobbyGamesParams
    ) {
        return getLobbyEntityGames(entity, lobbyId, params);
    }

    private static async getPlayerAndCheckSession(
        sessionId: string,
        playerCode: string,
        brandId: number
    ): Promise<Player> {
        const player = await getBrandPlayerService().getPlayer(brandId, playerCode);
        await createPlayerSessionFacade().find({ sessionId, playerCode, brandId });
        return player;
    }
}

const playerAPIService = lazy(() => new PlayerAPIService());

export function getPlayerAPIService(): PlayerAPIService {
    return playerAPIService.get();
}
