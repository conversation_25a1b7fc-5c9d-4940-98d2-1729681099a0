import { EntityGame, PlayerGameURLInfo } from "../../entities/game";
import { BrandEntity } from "../../entities/brand";
import { EntitySettings } from "../../entities/settings";
import { generateGameLauncherToken, PlayerShortLoginTokenData } from "../../utils/token";
import { getEntityDynamicDomainService } from "../entityDynamicDomainService";
import config from "../../config";
import { getDynamicDomainHost } from "./getDynamicDomainHost";
import { UrlPlaceholders } from "./urlPlaceholders";
import { ClientPayload } from "./getGameURLInfo";

function replaceUrlPlaceholders(stringToReplace: string, dynamicDomain: string, launcherToken?: string): string {
    if (stringToReplace.toString().match(UrlPlaceholders.REGEXP)) {
        stringToReplace = stringToReplace
            .replace(UrlPlaceholders.DYNAMIC_DOMAIN, dynamicDomain)
            .replace(UrlPlaceholders.LAUNCHER_TOKEN, launcherToken);
    }
    return stringToReplace;
}

export async function getLauncherGameUrl(entityGame: EntityGame,
                                         brand: BrandEntity,
                                         entitySettings: EntitySettings,
                                         player: PlayerShortLoginTokenData,
                                         isLobby: boolean,
                                         request: ClientPayload): Promise<PlayerGameURLInfo> {
    const token = await generateGameLauncherToken({
        brandId: brand.id,
        player,
        isLobby,
        gameCode: entityGame.game.code,
        request
    });

    const dynamicDomain = await getEntityDynamicDomainService().get(brand);
    const dynamicDomainHost = await getDynamicDomainHost(entitySettings, dynamicDomain, request.ip);
    const url = replaceUrlPlaceholders(config.gameLauncherUrlTemplate, dynamicDomainHost, token);

    return { url, token };
}
