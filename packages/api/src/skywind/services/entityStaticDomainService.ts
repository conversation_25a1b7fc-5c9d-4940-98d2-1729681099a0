import EntityCache from "../cache/entity";
import { StaticDomain } from "../entities/domain";
import { BaseEntity, ChildEntity, EntityInfo } from "../entities/entity";
import * as Errors from "../errors";
import { Models } from "../models/models";
import { StaticDomainService, getStaticDomainService } from "./domain";
import { default as EntitySettingsService } from "./settings";

export const getEntityStaticDomainService = () => new EntityStaticDomainService(getStaticDomainService());

class EntityStaticDomainService {
    constructor(private domainService: StaticDomainService) {
    }

    public async set(entity: BaseEntity, domainId: number): Promise<StaticDomain> {
        const domain = await this.domainService.findOne(domainId);

        await validateStaticDomain(entity, domain);

        entity.staticDomainId = domain.id;
        await Models.EntityModel.update({ staticDomainId: domain.id } as any, { where: { id: entity.id } });

        EntityCache.reset();

        return domain;
    }

    public async reset(entity: BaseEntity): Promise<StaticDomain> {

        await Models.EntityModel.update({ staticDomainId: null } as any, { where: { id: entity.id } });

        entity.staticDomainId = undefined;

        EntityCache.reset();

        return this.get(entity);
    }

    public async get(entity: BaseEntity): Promise<StaticDomain> {
        let parent = entity;

        while (!parent.isMaster() && !parent.staticDomainId) {
            parent = (parent as ChildEntity).getParent();
        }

        const domainId = parent.staticDomainId;
        if (domainId) {
            return this.domainService.findOne(domainId);
        }
    }

    public async setTags(entity: BaseEntity, tags: string[]): Promise<EntityInfo> {

        entity.staticDomainTags = tags;
        await entity.save().finally(() => EntityCache.reset());
        return entity.toInfo();
    }

    public async resetTags(entity: BaseEntity): Promise<EntityInfo> {
        entity.staticDomainTags = null;
        await entity.save().finally(() => EntityCache.reset());
        return entity.toInfo();
    }

}

export function isStaticDomainEnabled(domain: string, staticDomainTags?: string[]): boolean {
    if (!staticDomainTags || !staticDomainTags.length) {
        return true;
    }
    const cleanedDomain = domain.toLowerCase();
    for (const domainTag of staticDomainTags) {
        if (cleanedDomain.includes(domainTag.toLowerCase())) {
            return true;
        }
    }
    return false;
}

export async function validateStaticDomain(entity: BaseEntity, domain: StaticDomain) {
    if (!isStaticDomainEnabled(domain.domain, entity.staticDomainTags)) {
        throw new Errors.ValidationError(`Domain is not valid. Allowed tags - ${entity.staticDomainTags}`);
    }
    if (!entity.isMaster()) {
        const parentSettings = await new EntitySettingsService((entity as ChildEntity).getParent()).get();
        if (parentSettings.allowedStaticDomainsForChildId) {
            if (parentSettings.allowedStaticDomainsForChildId.indexOf(domain.id) === -1) {
                throw new Errors.ValidationError("Static domains restricted in parent entities");
            }
        }
    }
}
