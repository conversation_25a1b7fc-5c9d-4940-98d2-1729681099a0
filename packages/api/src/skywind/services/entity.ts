import { DestroyOptions, ForeignKeyConstraintError, Transaction, UpdateOptions, OrderItem } from "sequelize";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import {
    BaseEntity,
    ChildEntity,
    Entity,
    ENTITY_TYPE,
    EntityBalance,
    EntityBalances,
    EntityInfo,
    EntityKeyPath,
    EntityShortInfo,
    EntityStatus,
    EntityWitPathInfo,
    FindEntityOptions,
    MasterEntity,
    MerchantEntityInfo,
    MIGRATION_STATUS,
    StructureInfoOptions,
    WEBSITE_WHITELISTED_CHECK_LEVEL,
    WithBalances,
    WithDefaultBalance
} from "../entities/entity";
import { EntityModel as EntityDBInstance } from "../models/entity";
import { BrandEntity } from "../entities/brand";
import { FindOptions, Player } from "../entities/player";
import * as Errors from "../errors";
import { default as EntitySettingsService, getEntitySettings } from "./settings";
import { sequelize as db } from "../storage/db";
import { EntitySettings } from "../entities/settings";
import { BrandGGRDBInstance } from "../models/aggrWinBetsByBrand";
import { findOne as findMerchantBalance, getMerchantGGRForDefaultCurrency } from "../cache/merchantBalance";
import EntityCache from "../cache/entity";
import { COUNTRIES, COUNTRIES_CODES, LANGUAGES } from "../utils/common";
import * as MerchantTypesCache from "../cache/merchantTypes";
import { validateEntityStatus, validateEntityTestStatus } from "../utils/validateEntityStatus";
import { getMerchantCRUDService } from "./merchant";
import EntityFinance from "./entityFinance";
import { favoriteGamesCache } from "./gameCategory/gameCategoryGamesService";
import {
    BaseEntityWallet,
    BrandEntityWallet,
    EntityWallet,
    MasterEntityWallet,
    WalletErrors
} from "@skywind-group/sw-management-wallet";
import { EntityWalletProxy } from "./entityWalletProxy";
import { Balance, Balances } from "@skywind-group/sw-management-playservice";
import { defaultOperatorInfoUpdater } from "./gameauth/defaultOperatorInfoUpdater";
import { getDeploymentGroupService } from "./deploymentGroup";
import { getAvailableSiteService } from "./availableSites";
import { MerchantInfo } from "../entities/merchant";
import { getBrandPlayerService } from "./brandPlayer";
import * as MerchantCache from "../../skywind/cache/merchant";
import logger from "../utils/logger";
import * as LobbyCache from "../cache/lobby";
import { Models } from "../models/models";
import { MerchantType } from "../entities/merchantType";

const EntityModel = Models.EntityModel;
const BrandGGRModel = Models.BrandGGRModel;

const languagesCodes = Object.keys(LANGUAGES);

export const PATH_CHAR = ":";
const ENTITY_STRUCTURE_SORT_ORDER: OrderItem[] = [["id", "ASC"]];
const GGRModel = BrandGGRModel;
const log = logger("entity-service");

abstract class BaseEntityImpl implements BaseEntity {
    public id: number;
    public type: string = ENTITY_TYPE.ENTITY;
    public name: string;
    public description: string;
    public status: string;
    public key: string;
    public path: string;
    public title: string;
    public dynamicDomainId: number;
    public prevDynamicDomainId: number;
    public migrationStatus: MIGRATION_STATUS;
    public staticDomainId: number;
    public staticDomainPoolId: number;
    public dynamicDomainPoolId: number;
    public environment: string;
    public domains: string[];
    public staticDomainTags: string[];
    private version: number = 0;
    public checkWebSiteWhitelisted: string | null = null;
    public isTest: boolean;
    public deploymentGroupId?: number;

    constructor(item?) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.type = item.get("type");
        this.name = item.get("name");
        this.status = item.get("status");
        this.description = item.get("description");
        this.key = item.get("key");
        this.path = item.get("path");
        this.title = item.get("title");
        this.dynamicDomainId = item.get("dynamicDomainId");
        this.prevDynamicDomainId = item.get("prevDynamicDomainId");
        this.migrationStatus = item.get("migrationStatus");
        this.staticDomainId = item.get("staticDomainId");
        this.staticDomainPoolId = item.get("staticDomainPoolId");
        this.dynamicDomainPoolId = item.get("dynamicDomainPoolId");
        this.environment = item.get("environment");
        this.domains = item.get("domains");
        this.version = item.get("version");
        this.staticDomainTags = item.get("staticDomainTags");
        this.checkWebSiteWhitelisted = item.get("checkWebSiteWhitelisted");
        this.isTest = item.get("isTest");
        this.deploymentGroupId = item.get("deploymentGroupId");
    }

    public get inheritedDynamicDomainId() {
        let parent: BaseEntity = this;
        while (!parent.isMaster() && !parent.dynamicDomainId) {
            parent = (parent as ChildEntity).getParent();
        }

        return parent.dynamicDomainId;
    }

    public get inheritedDynamicDomainPoolId() {
        let parent: BaseEntity = this;
        while (!parent.isMaster() && !parent.dynamicDomainPoolId) {
            parent = (parent as ChildEntity).getParent();
        }

        return parent.dynamicDomainPoolId;
    }

    public get inheritedEnvironment() {
        let parent: BaseEntity = this;

        while (!parent.isMaster() && !parent.environment) {
            parent = (parent as ChildEntity).getParent();
        }

        return parent.environment;
    }

    public get inheritedWebSiteWhitelistedCheck() {
        let parent: BaseEntity = this;

        while (!parent.isMaster() && parent.checkWebSiteWhitelisted === null) {
            parent = (parent as ChildEntity).getParent();
        }

        return parent.checkWebSiteWhitelisted;
    }

    public get inheritedTestMode() {
        let parent: BaseEntity = this;
        while (!parent.isMaster() && !parent.isTest) {
            parent = (parent as ChildEntity).getParent();
        }

        return parent.isTest || false;
    }

    public async toInfo(decryptId?: boolean): Promise<EntityInfo> {
        const entityInfo: EntityInfo = {
            id: this.id,
            type: this.type,
            name: this.name,
            status: this.status,
            key: this.key,
            isTest: this.isTest
        };

        if (this.description) {
            entityInfo.description = this.description;
        }

        if (this.title) {
            entityInfo.title = this.title;
        }

        if (this.domains) {
            entityInfo.domains = this.domains;
        }

        if (!this.isMaster()) {
            const parent: BaseEntity = this;
            const parentDomains = (parent as ChildEntity).getParent().getDomains();
            if (parentDomains.length) {
                entityInfo.parentDomains = parentDomains;
            }
        }

        if (this.dynamicDomainId) {
            entityInfo.dynamicDomainId = this.dynamicDomainId;
            entityInfo.environment = this.environment;
        }

        if (this.staticDomainId) {
            entityInfo.staticDomainId = this.staticDomainId;
        }

        if (!this.isBrand()) {
            entityInfo.merchantTypes = await this.getMerchantTypes();
        }

        if (this.staticDomainTags) {
            entityInfo.staticDomainTags = this.staticDomainTags;
        }

        if (this.deploymentGroupId) {
            const deploymentGroupItem = await getDeploymentGroupService().getDeploymentGroup(this.deploymentGroupId);
            entityInfo.deploymentGroup = deploymentGroupItem;
        }

        if (decryptId) {
            entityInfo.decryptedBrand = this.id;
        }

        return Promise.resolve(entityInfo);
    }

    public async toInfoWithBalances(decryptId?: boolean): Promise<EntityInfo & WithBalances> {
        return await this.toInfo(decryptId) as EntityInfo & WithBalances;
    }

    public async structureToInfo(options: StructureInfoOptions = {}): Promise<EntityInfo> {
        const basePath = options.basePath || this.path;

        const entityInfo: EntityInfo = await this.toInfo(options.decryptId);
        entityInfo.path = makeRelativePath(this, basePath);

        return entityInfo;
    }

    public structureToShortInfo(options: StructureInfoOptions = {}): EntityShortInfo {
        if (!options.basePath) {
            options.basePath = this.path;
        }

        const info: EntityShortInfo = {
            id: this.id,
            name: this.name,
            title: this.title,
            type: this.type,
            path: makeRelativePath(this, options.basePath),
            key: this.key,
            isTest: !!this.isTest,
            status: this.status
        };

        if (this.description) {
            info.description = this.description;
        }

        if (options.decryptId) {
            info.decryptedBrand = this.id;
        }

        if (options.additionalFields) {
            for (const field of options.additionalFields) {
                if (this[field]) {
                    info[field] = this[field] as never;
                }
            }
        }

        return info;
    }

    public async save(transaction?: Transaction): Promise<this> {
        // TODO - we may need to reflect the changes on the child entities
        const oldVersion = this.version;
        this.version = oldVersion + 1;

        const updateOptions: UpdateOptions = {
            where: {
                id: this.id,
                version: oldVersion,
            }
        };

        if (transaction) {
            updateOptions["transaction"] = transaction;
        }

        try {
            // TODO: need to fix 'private "version" param of this class'
            const [affectedCount]: [number] = await EntityModel.update(this as any, updateOptions);
            if (affectedCount !== 1) {
                return Promise.reject(new Errors.OptimisticLockException());
            }
        } catch (err) {
            this.version = oldVersion;
            return Promise.reject(err);
        } finally {
            await LobbyCache.reset();
            await favoriteGamesCache.reset();
        }

        return this;
    }

    public getVersion(): number {
        return this.version;
    }

    public isMaster(): boolean {
        return false;
    }

    public isBrand(): boolean {
        return false;
    }

    public isSuspended(): boolean {
        return this.status === EntityStatus.SUSPENDED || this.status === EntityStatus.BLOCKED_BY_ADMIN;
    }

    public underMaintenance(): boolean {
        return this.status === EntityStatus.MAINTENANCE;
    }

    public find(options: FindEntityOptions): BaseEntity {
        let match: boolean = true;
        if (options.id) {
            if (options.id !== this.id) {
                match = false;
            }
        }
        if (options.path) {
            if (options.path !== this.path) {
                match = false;
            }
        }
        if (options.name) {
            if (options.name !== this.name) {
                match = false;
            }
        }
        if (options.key) {
            if (options.key !== this.key) {
                match = false;
            }
        }
        if (match) {
            return this;
        }
    }

    public get wallet(): EntityWallet {
        return new EntityWalletProxy(this, new BaseEntityWallet(this.id));
    }

    public abstract addCurrency(code: string): void;

    public abstract addCountry(code: string): void;

    public abstract addLanguage(code: string): void;

    public abstract updateCurrencyArray(array: Array<string>): void;

    public abstract updateLanguageArray(array: Array<string>): void;

    public abstract updateMerchantTypesArray(array: Array<string>): Promise<void>;

    public abstract removeCurrency(code: string): Promise<void>;

    public abstract removeCountry(code: string): void;

    public abstract removeLanguage(code: string): void;

    public abstract currencyExists(code: string): boolean;

    public abstract countryExists(code: string): boolean;

    public abstract languageExists(code: string): boolean;

    public abstract getCountries(): Array<string>;

    public abstract getCurrencies(): Array<string>;

    public abstract getLanguages(): Array<string>;

    public abstract fetchBalance(currency: string): Promise<EntityBalance>;

    public abstract fetchBalances(): Promise<EntityBalances>;

    public abstract getMerchantTypes(): Promise<string[]>;

    public abstract getChildInheritedMerchantTypes(): string[];

    public abstract addMerchantType(type: string | string[]): Promise<void>;

    public abstract merchantTypeExists(type: string): Promise<boolean>;

    public abstract removeMerchantType(type: string | string[]): Promise<void>;

    public getInheritedDomains() {
        return this.domains;
    }

    public domainExists(domain: string): boolean {
        if (!this.hasDomains()) {
            return true;
        }

        if (!domain) {
            return false;
        }

        return this.domains.some(entityDomain => entityDomain.toLowerCase() === domain.toLowerCase());
    }

    protected hasDomains(): boolean {
        return (Array.isArray(this.domains) && this.domains.length > 0);
    }

    public getDomains(): string[] {
        return this.domains || [];
    }
}

/**
 * Implementation of MasterEntity
 */
class MasterEntityImpl extends BaseEntityImpl implements MasterEntity {
    public id: number;
    public type: string = ENTITY_TYPE.ENTITY;
    public name: string;
    public status: string;
    public key: string;
    public path: string;
    public title: string;
    public dynamicDomainId: number;
    public dynamicDomainPoolId: number;
    public staticDomainId: number;
    public environment: string;
    public domains: string[];

    public child: Array<Entity> = [];

    constructor(item) {
        super(item);
        this.id = item.get("id");
        this.type = item.get("type");
        this.name = item.get("name");
        this.status = item.get("status");
        this.key = item.get("key");
        this.path = item.get("path");
        this.title = item.get("title");
        this.dynamicDomainId = item.get("dynamicDomainId");
        this.staticDomainId = item.get("staticDomainId");
        this.environment = item.get("environment");
        this.domains = item.get("domains");
    }

    public get inheritedDynamicDomainId() {
        return this.dynamicDomainId;
    }

    public get inheritedDynamicDomainPoolId() {
        return this.dynamicDomainPoolId;
    }

    public get inheritedEnvironment() {
        return this.environment;
    }

    public async toInfoWithBalances(decryptId?: boolean): Promise<EntityInfo & WithBalances> {
        return await this.toInfo(decryptId) as EntityInfo & WithBalances;
    }

    public async structureToInfo(options: StructureInfoOptions = {}): Promise<EntityInfo> {
        if (!options.basePath) {
            options.basePath = this.path;
        }

        const baseEntityInfo: EntityInfo = await this.toInfo(options.decryptId);
        baseEntityInfo.id = this.id;
        baseEntityInfo.path = makeRelativePath(this, options.basePath);

        if (this.child) {
            baseEntityInfo.child = [];
            const promises = [];
            for (let i = 0, length = this.child.length; i < length; i++) {
                promises.push(this.child[i].structureToInfo(options)
                    .then((entityInfo: EntityInfo) => baseEntityInfo.child[i] = entityInfo));
            }
            await Promise.all(promises);
        }
        return baseEntityInfo;
    }

    public structureToShortInfo(options: StructureInfoOptions = {}): EntityShortInfo {
        const shortInfo = super.structureToShortInfo(options);

        if (this.child) {
            shortInfo.child = this.child.map((child) => child.structureToShortInfo(options));
        }

        return shortInfo;
    }

    public find(options: FindEntityOptions): BaseEntity {
        let match: boolean = true;
        if (options.id) {
            if (options.id !== this.id) {
                match = false;
            }
        }
        if (options.path) {
            if (options.path !== this.path) {
                match = false;
            }
        }
        if (options.name) {
            if (options.name !== this.name) {
                match = false;
            }
        }
        if (options.key) {
            if (options.key !== this.key) {
                match = false;
            }
        }
        if (match) {
            return this;
        }

        for (const c of this.child) {
            const child = c.find(options);
            if (child) {
                return child;
            }
        }

        return undefined;
    }

    /**
     * isMaster - always return true as we are master entity
     *
     * @returns {boolean}
     */
    public isMaster(): boolean {
        return true;
    }

    public isBrand(): boolean {
        return false;
    }

    public isSuspended(): boolean {
        return this.status === EntityStatus.SUSPENDED || this.status === EntityStatus.BLOCKED_BY_ADMIN;
    }

    public underMaintenance(): boolean {
        return this.status === EntityStatus.MAINTENANCE;
    }

    /**
     * currencyExists - for master entity all currencies exists
     */
    public currencyExists(code: string): boolean {
        return true;
    }

    /**
     * countryExists - for master entity all countries exists
     */
    public countryExists(code: string): boolean {
        return true;
    }

    /**
     * languageExists - for master entity all languages exists
     */
    public languageExists(code: string): boolean {
        return true;
    }

    public get wallet(): EntityWallet {
        return new MasterEntityWallet();
    }

    public getCurrencies(): Array<string> {
        return Currencies.keys();
    }

    public getCountries(): Array<string> {
        return COUNTRIES_CODES;
    }

    public getLanguages(): Array<string> {
        return languagesCodes;
    }

    public async fetchBalance(currency: string): Promise<EntityBalance> {
        return {} as Balance;
    }

    public async fetchBalances(): Promise<EntityBalances> {
        return {};
    }

    public addCurrency(code: string): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public addCountry(code: string): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public addLanguage(code: string): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public updateCurrencyArray(array: Array<string>): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public updateLanguageArray(array: Array<string>): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public updateMerchantTypesArray(array: Array<string>): Promise<void> {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public async removeCurrency(code: string): Promise<void> {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public removeCountry(code: string): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public removeLanguage(code: string): void {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public getInheritedDomains() {
        return this.domains;
    }

    public domainExists(domain: string): boolean {
        if (!this.hasDomains()) {
            return true;
        }

        if (!domain) {
            return false;
        }

        return this.domains.some(entityDomain => entityDomain.toLowerCase() === domain.toLowerCase());
    }

    protected hasDomains(): boolean {
        return (Array.isArray(this.domains) && this.domains.length > 0);
    }

    public async getMerchantTypes(): Promise<string[]> {
        const merchantTypesInfo: MerchantType[] = await MerchantTypesCache.findAll();
        return merchantTypesInfo.map(merchantTypeInfo => merchantTypeInfo.type);
    }

    public getChildInheritedMerchantTypes(): string[] {
        const childTypes: string[] = [];
        for (const child of this.child) {
            childTypes.push(...child.getChildInheritedMerchantTypes());
        }
        return Array.from(new Set(childTypes));
    }

    public async addMerchantType(type: string | string[]) {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public removeMerchantType(type: string | string[]): Promise<void> {
        throw new Errors.ValidationError("Unavailable operation for the master entity");
    }

    public async merchantTypeExists(type: string): Promise<boolean> {
        const merchantTypes = await this.getMerchantTypes();
        return merchantTypes.some(merchantType => merchantType === type);
    }
}

interface MergeStateSupport {
    mergeWithParentState(): void;
}

const parentSymbol = Symbol("parentEntity");
const mergedSymbol = Symbol("merged");

class ChildEntityImpl extends BaseEntityImpl implements ChildEntity, MergeStateSupport {
    public parent: number;
    public defaultCurrency: string;
    public defaultCountry: string;
    public defaultLanguage: string;

    public countries: Array<string> = [];
    public currencies: Array<string> = [];
    public languages: Array<string> = [];

    public merchantTypes: string[] = [];

    private [parentSymbol]?: Entity;
    private [mergedSymbol]?: boolean;

    constructor(item?) {
        super(item);
        if (!item) {
            return;
        }
        this.parent = item.get("parent");

        this.defaultCurrency = item.get("defaultCurrency");
        this.defaultCountry = item.get("defaultCountry");
        this.defaultLanguage = item.get("defaultLanguage");

        this.countries = item.get("countries");
        this.currencies = item.get("currencies");
        this.languages = item.get("languages");

        this.merchantTypes = item.get("merchantTypes");
    }

    public async toInfo(decryptId?: boolean): Promise<EntityInfo> {
        const entityInfo = await super.toInfo(decryptId);
        entityInfo.defaultCurrency = this.defaultCurrency;
        entityInfo.defaultCountry = this.defaultCountry;
        entityInfo.defaultLanguage = this.defaultLanguage;
        entityInfo.countries = this.countries;
        entityInfo.currencies = this.currencies;
        entityInfo.languages = this.languages;
        return entityInfo;
    }

    public async toInfoWithBalances(decryptId?: boolean): Promise<EntityInfo & WithBalances> {
        const balances: Balances = await this.fetchBalances();
        const entityInfo: EntityInfo & WithBalances = await this.toInfo(decryptId) as EntityInfo & WithBalances;
        entityInfo.balances = balances;
        return entityInfo;
    }

    public async structureToInfo(options: StructureInfoOptions = {}): Promise<EntityInfo & WithDefaultBalance> {
        const entityInfo = await super.structureToInfo(options) as EntityInfo & WithDefaultBalance;
        entityInfo.defaultBalance = await this.fetchBalanceAndCurrency(entityInfo.defaultCurrency);
        return entityInfo;
    }

    public setParent(parent: Entity): void {
        this[parentSymbol] = parent;
        this.parent = parent.id;
        this.path = `${parent.path}${this.name}${PATH_CHAR}`;
    }

    public getParent(): Entity {
        return this[parentSymbol];
    }

    public isSuspended(): boolean {
        return super.isSuspended() ? super.isSuspended() : this.getParent().isSuspended();
    }

    public underMaintenance(): boolean {
        return super.underMaintenance() ? super.underMaintenance() : this.getParent().underMaintenance();
    }

    public isTopLevel(): boolean {
        const parent: Entity = this.getParent();
        return parent.isMaster();
    }

    public setDefaultCurrency(code: string): void {
        if (!this.currencyExists(code)) {
            // For now we add not found code to list
            this.addCurrency(code);
            // throw new Errors.CurrencyNotInListError(code);
        }
        this.defaultCurrency = code;
    }

    public addCurrency(code: string): void {
        if (!Currencies.exists(code)) {
            throw new Errors.CurrencyNotFoundError(code);
        }

        const parent = this.getParent();

        // parent does not allowed this currency code
        if (!parent.currencyExists(code)) {
            throw new Errors.CurrencyNotExistInParentError(code);
        }

        if (!this.currencyExists(code)) {
            this.currencies.push(code);
        }
    }

    public async removeCurrency(code: string): Promise<void> {
        if (this.defaultCurrency === code) {
            throw new Errors.CannotRemoveCurrencyDefaultError(code);
        }
        const { currency, balance } = await this.fetchBalanceAndCurrency(code);
        if (balance && balance > 0) {
            const financeService = new EntityFinance(this);
            await financeService.debit(currency, balance);
        }
        this.currencies = this.currencies.filter(c => c !== code);
    }

    public updateCurrencyArray(array: Array<string>): void {
        this.currencies = [];
        const parent = this.getParent();

        if (!Array.isArray(array)) {
            throw new Errors.CurrenciesIsNotArray();
        }

        for (const code of array) {
            if (!Currencies.exists(code)) {
                throw new Errors.CurrencyNotFoundError(code);
            }

            // parent does not allow this language code
            if (!parent.currencyExists(code)) {
                throw new Errors.CurrencyNotExistInParentError(code);
            }

            if (!this.currencyExists(code)) {
                this.currencies.push(code);
            }
        }
    }

    public currencyExists(code: string): boolean {
        return this.currencies.includes(code);
    }

    public getCurrencies(): Array<string> {
        return this.currencies;
    }

    public setDefaultCountry(code: string): void {
        this.defaultCountry = code;
    }

    public addCountry(code: string): void {
        if (!COUNTRIES[code]) {
            throw new Errors.CountryNotFoundError(code);
        }
        if (!this.countryExists(code)) {
            this.countries.push(code);
        }
    }

    public removeCountry(code: string): void {
        this.countries = this.countries.filter(country => country !== code);
    }

    public countryExists(code: string): boolean {
        return this.countries.includes(code);
    }

    public getCountries(): Array<string> {
        return this.countries;
    }

    public setDefaultLanguage(code: string): void {
        if (!this.languageExists(code)) {
            // For now we add not found code to list
            this.addLanguage(code);
            // throw new Errors.LanguageNotInListError(code);
        }

        this.defaultLanguage = code;
    }

    public addLanguage(code: string): void {
        if (!LANGUAGES[code]) {
            throw new Errors.LanguageNotFoundError(code);
        }

        const parent = this.getParent();

        // parent does not allowed this language code
        if (!parent.languageExists(code)) {
            throw new Errors.LanguageNotExistInParentError(code);
        }

        if (!this.languageExists(code)) {
            this.languages.push(code);
        }
    }

    public removeLanguage(code: string): void {
        if (this.defaultLanguage === code) {
            throw new Errors.CannotRemoveLanguageDefaultError(code);
        }
        this.languages = this.languages.filter(language => language !== code);
    }

    public updateLanguageArray(array: Array<string>): void {
        this.languages = [];
        const parent = this.getParent();

        if (!Array.isArray(array)) {
            throw new Errors.LanguagesIsNotArray();
        }

        for (const code of array) {
            if (!LANGUAGES[code]) {
                throw new Errors.LanguageNotFoundError(code);
            }

            // parent does not allowed this language code
            if (!parent.languageExists(code)) {
                throw new Errors.LanguageNotExistInParentError(code);
            }

            if (!this.languageExists(code)) {
                this.languages.push(code);
            }
        }
    }

    public async updateMerchantTypesArray(newMerchantTypes: Array<string>): Promise<void> {
        this.merchantTypes = [];
        const parent = this.getParent();

        if (!Array.isArray(newMerchantTypes)) {
            throw new Errors.MerchantTypesIsNotArray();
        }

        for (const type of newMerchantTypes) {
            if (!await parent.merchantTypeExists(type)) {
                throw new Errors.MerchantTypeIsNotSupportedByParentError(type);
            }

            if (!this.merchantTypes.includes(type)) {
                this.merchantTypes.push(type);
            }
        }
    }

    public languageExists(code: string): boolean {
        return this.languages.includes(code);
    }

    public getLanguages(): Array<string> {
        return this.languages;
    }

    public mergeWithParentState(): void {
        if (!this[mergedSymbol]) {
            const p: Entity & MergeStateSupport = this.getParent() as Entity & MergeStateSupport;
            if (p && p.getCountries && p.getCurrencies && p.mergeWithParentState) {
                const parent = p as ChildEntity & MergeStateSupport;
                parent.mergeWithParentState();
                this.countries = this.countries.filter(
                    x => parent.countryExists(x));
                this.currencies = this.currencies.filter(
                    x => parent.currencyExists(x));
                this.languages = this.languages.filter(
                    x => parent.languageExists(x));
            }
            this[mergedSymbol] = true;
        }
    }

    public async fetchBalance(currency: string): Promise<EntityBalance> {
        const balance = await this.fetchBalanceAndCurrency(currency);
        return { main: balance.balance };
    }

    public fetchBalances(): Promise<EntityBalances> {
        return Promise.all(this.currencies.map((currency: string) => this.fetchBalanceAndCurrency(currency)))
            .then((values: any[]) => values.reduce((balances: Balances, value: any) => {
                balances[value.currency] = { main: value.balance };
                return balances;
            }, {}));
    }

    private async fetchBalanceAndCurrency(currency: string): Promise<any> {
        let balance = await this.wallet.getBalance(currency);
        if (this.type === ENTITY_TYPE.MERCHANT && currency !== "BNS") {
            let brandGGR;
            if (this.defaultCurrency === currency) {
                brandGGR = await getMerchantGGRForDefaultCurrency(this.id, currency);
            } else {
                brandGGR = await (this as any as BrandEntityImpl).getMerchantGGR(currency);
            }
            if (brandGGR > 0) {
                balance = balance - brandGGR;
            }
        }
        return {
            currency: currency,
            balance: Currencies.get(currency).toMajorUnits(balance),
        };
    }

    public getInheritedDomains(): string[] {
        if (!this.hasDomains()) {
            return this.getParent().getInheritedDomains();
        }
        return this.domains;
    }

    public domainExists(domain: string): boolean {
        if (!this.hasDomains()) {
            return this.getParent().domainExists(domain);
        }

        if (!domain) {
            return false;
        }

        return this.domains.some(entityDomain => entityDomain.toLowerCase() === domain.toLowerCase());
    }

    public async getMerchantTypes(): Promise<string[]> {
        return this.merchantTypes || [];
    }

    public getChildInheritedMerchantTypes(): string[] {
        return this.merchantTypes || [];
    }

    public async addMerchantType(type: string | string[]): Promise<void> {
        const types = Array.isArray(type) ? type : [ type ];
        const parent = this.getParent();

        const notSupported = [];
        for (const type of types) {
            if (!(await parent.merchantTypeExists(type))) {
                notSupported.push(type);
            }
        }
        if (notSupported.length) {
            return Promise.reject(new Errors.MerchantTypeIsNotSupportedByParentError(notSupported));
        }

        if (!this.merchantTypes) {
            this.merchantTypes = [];
        }

        const existing = types.filter((t) => this.merchantTypes.includes(t));
        if (existing.length) {
            return Promise.reject(new Errors.MerchantTypeExists(existing));
        }

        this.merchantTypes.push(...types);
    }

    public async merchantTypeExists(type: string, own = false): Promise<boolean> {
        if (own) {
            return this.merchantTypes && this.merchantTypes.includes(type);
        } else {
            const supportedTypes = await this.getMerchantTypes();
            return supportedTypes.includes(type);
        }
    }

    public async removeMerchantType(type: string | string[]): Promise<void> {
        const types = Array.isArray(type) ? type : [ type ];

        const notExists = types.filter((v) => !this.merchantTypes || !this.merchantTypes.includes(v));
        if (notExists.length) {
            throw new Errors.MerchantTypeIsNotAvailable(notExists);
        }
        this.merchantTypes = this.merchantTypes.filter(merchantType => !types.includes(merchantType));
    }

    public getDomains(): string[] {
        return [ ...new Set([ ...(this.domains || []), ...this.getParent().getDomains() ]) ];
    }
}

export class EntityImpl extends ChildEntityImpl implements Entity {
    public child: Array<Entity> = [];

    constructor(item?) {
        super(item);
    }

    public async structureToInfo(options: StructureInfoOptions = {}): Promise<EntityInfo & WithDefaultBalance> {
        if (!options.basePath) {
            options.basePath = this.path;
        }

        const info: EntityInfo & WithDefaultBalance = await super.structureToInfo(options);

        if (this.child) {
            info.child = [];
            const promises = [];
            for (let i = 0, length = this.child.length; i < length; i++) {
                promises.push(this.child[i].structureToInfo(options)
                    .then((entityInfo: EntityInfo) => info.child[i] = entityInfo));
            }
            await Promise.all(promises);
        }

        return info;
    }

    public structureToShortInfo(options: StructureInfoOptions = {}): EntityShortInfo {
        const shortInfo = super.structureToShortInfo(options);

        if (this.child) {
            shortInfo.child = this.child.map((child) => child.structureToShortInfo(options));
        }

        return shortInfo;
    }

    public find(options: FindEntityOptions): BaseEntity {
        const item: BaseEntity = super.find(options);
        if (item) {
            return item;
        }

        for (const c of this.child) {
            const child = c.find(options);
            if (child) {
                return child;
            }
        }
        return undefined;
    }

    public removeCountry(code: string): void {
        const children = this.child.filter(child => child.countryExists(code));
        if (children.length) {
            throw new Errors.ValidationError(
                `This country code ${code} in use in child entities. You cannot remove it.`,
                undefined,
                {
                    children: children.map(child => ({
                        title: child.title,
                        path: child.path,
                        name: child.name
                    }))
                });
        }

        super.removeCountry(code);
    }

    public async removeCurrency(code: string): Promise<void> {
        const children = this.child.filter(child => child.currencyExists(code));
        if (children.length) {
            throw new Errors.ValidationError(
                `This currency code ${code} in use in child entities. You cannot remove it.`,
                undefined,
                {
                    children: children.map(child => ({
                        title: child.title,
                        path: child.path,
                        name: child.name
                    }))
                });
        }

        await super.removeCurrency(code);
    }

    public removeLanguage(code: string): void {
        const children = this.child.filter(child => child.languageExists(code));
        if (children.length) {
            throw new Errors.ValidationError(
                `This language code ${code} in use in child entities. You cannot remove it.`,
                undefined,
                {
                    children: children.map(child => ({
                        title: child.title,
                        path: child.path,
                        name: child.name
                    }))
                });
        }

        super.removeLanguage(code);
    }

    public getChildInheritedMerchantTypes(): string[] {
        if (this.merchantTypes) {
            return this.merchantTypes;
        }
        const childTypes: string[] = [];
        for (const child of this.child) {
            childTypes.push(...child.getChildInheritedMerchantTypes());
        }
        return Array.from(new Set(childTypes));
    }

    public async addMerchantType(type: string | string[]): Promise<void> {
        const newMerchantTypes = [].concat(this.merchantTypes || []).concat(Array.isArray(type) ? type : [ type ]);
        const childTypes: string[] = [];
        for (const child of this.child) {
            childTypes.push(...child.getChildInheritedMerchantTypes());
        }
        const missingTypes = Array.from(new Set(childTypes)).filter((type) => !newMerchantTypes.includes(type));
        if (missingTypes.length) {
            return Promise.reject(new Errors.ValidationError(
                `Merchant types [${missingTypes}] in use in child entities. These types should be added to entity.`));
        }

        return super.addMerchantType(type);
    }

    public async removeMerchantType(type: string | string[]): Promise<void> {
        const types = Array.isArray(type) ? type : [ type ];
        const children = [];
        for (const child of this.child) {
            const childTypes = [];
            for (const type of types) {
                if (await child.merchantTypeExists(type, true)) {
                    childTypes.push(type);
                }
            }
            if (childTypes.length) {
                children.push({
                    title: child.title,
                    path: child.path,
                    name: child.name,
                    types: childTypes
                });
            }
        }
        if (children.length) {
            const childTypes = Array.from(new Set([].concat(...children.map((v) => v.types))));
            return Promise.reject(new Errors.ValidationError(
                `Merchant types [${childTypes}] in use in child entities. You cannot remove it.`,
                undefined,
                { children }));
        }

        return super.removeMerchantType(type);
    }

    public async updateMerchantTypesArray(newMerchantTypes: Array<string>): Promise<void> {
        if (!newMerchantTypes.length) {
            this.merchantTypes = undefined;
        }

        const currentMerchantTypes = this.merchantTypes || [];

        const addedMerchantTypes = newMerchantTypes.filter((type) => !currentMerchantTypes.includes(type));
        if (addedMerchantTypes.length) {
            await this.addMerchantType(addedMerchantTypes);
        }

        const removedMerchantTypes = currentMerchantTypes.filter((type) => !newMerchantTypes.includes(type));
        if (removedMerchantTypes.length) {
            await this.removeMerchantType(removedMerchantTypes);
        }
    }
}

class BrandEntityImpl extends ChildEntityImpl implements BrandEntity {

    public isMerchant: boolean;
    public isTest: boolean;

    constructor(item?) {
        super(item);
        if (!item) {
            this.type = ENTITY_TYPE.BRAND;
            return;
        }
        this.isMerchant = this.type === ENTITY_TYPE.MERCHANT;
        this.isTest = item.get("isTest");
    }

    public isBrand(): boolean {
        return true;
    }

    public get wallet(): EntityWallet {
        return new EntityWalletProxy(this, new BrandEntityWallet(this.id));
    }

    public findPlayer(options: FindOptions): Promise<Player> {
        return getBrandPlayerService().findOne(this, options);
    }

    public async toInfo(decryptId?: boolean): Promise<EntityInfo> {
        const entityInfo = await super.toInfo(decryptId);
        entityInfo.isMerchant = this.isMerchant;
        entityInfo.isTest = this.isTest;
        return entityInfo;
    }

    public async getMerchantGGR(currency: string): Promise<number> {
        const brandGGRInstance: BrandGGRDBInstance =
            await GGRModel.findOne({ where: { brandId: this.id, currency: currency } });
        if (brandGGRInstance) {
            return brandGGRInstance.get("revenue") || 0;
        }
        return 0;
    }
}

/**
 * @deprecated
 */
export class LiveStudioImpl extends EntityImpl {
    constructor(item?) {
        super(item);
        this.type = "liveStudio";
    }
}

/**
 * Create database structure init super user
 *
 */
export async function initDB(): Promise<void> {
    let entityDB;
    try {
        entityDB = await EntityModel.findOne({
            where: { path: ":" },
        });
    } catch (err) {
        return Promise.reject(err);
    }

    if (!entityDB) {
        entityDB = await EntityModel.create({
            name: "MASTER",
            type: ENTITY_TYPE.ENTITY,
            status: "normal",
            key: "aaa11200-19f1-48c1-a78c-3a3d56095f38",
            path: ":",
            defaultCountry: "N/A",
            defaultCurrency: "N/A",
            defaultLanguage: "N/A",
            countries: [ "N/A" ],
            currencies: [ "N/A" ],
            languages: [ "N/A" ],
        });
    }
    const settings = await getEntitySettings(entityDB.get("path"));
    if (!settings || !settings.emailTemplates) {
        const settingsService = new EntitySettingsService(entityDB);
        await settingsService.patch({
            emailTemplates: {
                passwordRecovery: {
                    from: "Skywind <<EMAIL>>",
                    subject: "Skywind Password Reset",
                    html: `
{{username}},
<p>
To reset your password, please use the following link: <a href="{{domain}}/?token={{token}}">click to reset
</a>.
It will expire in 15 minutes — after that you'll need to request a new one.
</p>
<p>
If you didn't request this change, please let us know by replying to this email.
</p>
`,
                },
                changeEmail: {
                    from: "Skywind <<EMAIL>>",
                    subject: "Skywind Change Email Confirmation",
                    html: `
{{username}},
<p>
To confirm your email, please use the following link: <a href="{{domain}}/?token={{token}}">click to confirm
</a>.
It will expire in 15 minutes — after that you'll need to request a new one.
</p>
<p>
If you didn't request this change, please let us know by replying to this email.
</p>
`,
                },
            },
        });
    }
}

export function createEntity(item: EntityDBInstance): any {
    return createEntityByType(item.parent !== null, item.get("type"), item);
}

export function createEntityByType(hasParent: boolean, type: string, item?: EntityDBInstance): any {
    if (type === ENTITY_TYPE.ENTITY) {
        if (hasParent) {
            return new EntityImpl(item);
        } else {
            return new MasterEntityImpl(item);
        }
    }
    // @deprecated
    if (type === "liveStudio") {
        return new LiveStudioImpl(item);
    }
    return new BrandEntityImpl(item);
}

async function find(transaction?: Transaction): Promise<Array<BaseEntity>> {
    const list: EntityDBInstance[] = await EntityModel.findAll({ order: ENTITY_STRUCTURE_SORT_ORDER, transaction });

    return list.map(createEntity);
}

export async function loadHierarchy(transaction?: Transaction): Promise<BaseEntity> {
    const list = await find(transaction);
    // build entities list according to the path
    const entities = {};
    let master: BaseEntity = null;
    list.forEach((entity => entities[entity.path] = entity));

    // run on each of the entities and find the parent
    list.forEach((entity: Entity & MergeStateSupport) => {
        // if we have master we don't have any parent
        if (entity.isMaster()) {
            master = entity;
            return;
        }

        // calculate the parent path
        const path = entity.path;
        const i = path.lastIndexOf(PATH_CHAR, path.length - 2);
        const parentPath: string = i < 0 ? path : path.substring(0, i + 1);
        // get the parent entity
        const parent = entities[parentPath];

        // add the current entity to his parent one as child
        if (parent) {
            parent.child.push(entity);
            entity.setParent(parent);
            entity.mergeWithParentState();
        }
    });
    return master;
}

export async function findOne<T extends BaseEntity>(options: FindEntityOptions,
                                                    path?: string,
                                                    raiseErrorIfNotFound: boolean = false,
                                                    transaction?: Transaction): Promise<T> {
    return findOneInHierarchy<T>(options, () => loadHierarchy(transaction), path, raiseErrorIfNotFound);
}

export async function findOneInHierarchy<T extends BaseEntity>(options: FindEntityOptions,
                                                               loadHierarchy: () => Promise<BaseEntity>,
                                                               path?: string,
                                                               raiseErrorIfNotFound: boolean = false): Promise<T> {
    if (!path) {
        path = PATH_CHAR;
    }
    const root: BaseEntity = await loadHierarchy();
    const base: BaseEntity = root.find({ path: path });
    const result: BaseEntity = base && base.find(options);

    if (!result && raiseErrorIfNotFound) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }

    return result as T;
}

export async function findMaster(): Promise<MasterEntity> {
    const master: EntityDBInstance = await EntityModel.findOne({ where: { path: ":" } });
    return new MasterEntityImpl(master);
}

function makeRelativePath(entity: BaseEntity,
                          basePath: string): string {
    let relativePath: string;
    if (entity.path.startsWith(basePath)) {
        relativePath = entity.path.substring(basePath.length);
        if (!relativePath) {
            relativePath = PATH_CHAR;
        }
    } else {
        relativePath = entity.path;
    }

    return relativePath;
}

/**
 * info - get information about specific entity
 * @param keyEntity
 * @param options
 * @returns {Promise<EntityInfo>}
 */
export function info(keyEntity: BaseEntity,
                     options: FindEntityOptions): Promise<EntityInfo & WithBalances> {
    const entity: BaseEntity = keyEntity.find(options) as ChildEntity;
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }

    return entity.toInfoWithBalances();
}

export async function suspend(keyEntity: BaseEntity, options: FindEntityOptions): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, EntityStatus.SUSPENDED);
}

export async function restore(keyEntity: BaseEntity, options: FindEntityOptions): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, EntityStatus.NORMAL, EntityStatus.SUSPENDED);
}

export async function turnMaintenanceOn(keyEntity: BaseEntity, options: FindEntityOptions): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, EntityStatus.MAINTENANCE);
}

export async function turnMaintenanceOff(keyEntity: BaseEntity, options: FindEntityOptions): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, EntityStatus.NORMAL, EntityStatus.MAINTENANCE);
}

export async function turnTestStatusOn(keyEntity: BaseEntity,
                                       options: FindEntityOptions,
                                       forceFlag: boolean): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, EntityStatus.TEST, undefined, forceFlag);
}

export async function turnTestStatusOff(keyEntity: BaseEntity, options: FindEntityOptions): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, EntityStatus.NORMAL, EntityStatus.TEST);
}

export async function updateEntityStatus(
    keyEntity: BaseEntity,
    options: FindEntityOptions,
    status: EntityStatus): Promise<EntityInfo> {
    return changeStatus(keyEntity, options, status);
}

async function changeStatus(keyEntity: BaseEntity,
                            options: FindEntityOptions,
                            status: string,
                            fromStatus?: string,
                            forceFlag?: boolean): Promise<EntityInfo & WithBalances> {
    const entity = await EntityCache.findOne<Entity>(options, keyEntity.path, true, true);
    if (!keyEntity.isMaster()) {
        validateEntityStatus(entity);
    }
    if (entity.status !== EntityStatus.NORMAL && fromStatus && entity.status !== fromStatus) {
        throw new Errors.EntityStatusNotMatchError(entity.status);
    }

    if (status === EntityStatus.TEST) {
        await validateEntityTestStatus(entity);
    }
    if (entity.status === EntityStatus.NORMAL && status === EntityStatus.TEST && !forceFlag) {
        throw new Errors.ValidationError(
            "Can't change entity status from normal to test without force flag"
        );
    }
    entity.status = status;
    const result = await entity.save().finally(() => EntityCache.reset());
    if (entity.isSuspended()) {
        defaultOperatorInfoUpdater.get().removeByPath(entity.path);
    }
    return result.toInfoWithBalances();
}

/**
 * removeEntity - remove entity from a tree
 * @param {BaseEntity} keyEntity
 * @param {FindEntityOptions} options
 * @returns {Promise<EntityInfo & WithBalances>}
 */
export async function removeEntity<T extends BaseEntity>(
    keyEntity: BaseEntity, options: FindEntityOptions): Promise<EntityInfo & WithBalances> {

    const entity: T = await keyEntity.find(options) as T;

    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }

    try {
        await db.transaction(async (transaction: Transaction): Promise<any> => {
            await (new EntitySettingsService(entity)).reset();
            const destroyParams: DestroyOptions = {
                where: {
                    id: entity.id,
                },
                transaction: transaction
            };
            if (entity.isBrand() && (entity as any).isMerchant) {
                await getMerchantCRUDService().remove(entity as any, transaction);
            }
            await EntityModel.destroy(destroyParams);
            await LobbyCache.reset();
            await favoriteGamesCache.reset();
        });
    } catch (err) {
        if (err instanceof ForeignKeyConstraintError) {
            const tableName = err.message.split(" ").reverse()[0].replace(/"/g, "");
            return Promise.reject(new Errors.EntityNotEmpty(tableName));
        }
        return Promise.reject(err);
    }
    return entity.toInfoWithBalances();
}

export async function validateEntityBalance(entity: BaseEntity, currencyCode: string): Promise<void> {
    if (!entity.currencyExists(currencyCode)) {
        return Promise.reject(new Errors.CurrencyNotExistError(currencyCode));
    }
    const settings: EntitySettings = await getEntitySettings(entity.path);
    if (settings && settings.isAccountBlockingEnabled) {
        const balance = await findMerchantBalance(entity, currencyCode);
        if (balance.main <= 0) {
            return Promise.reject(new WalletErrors.InsufficientEntityBalanceError());
        }
    }
}

export function validateMaintenance(entity: BaseEntity): void {
    if (entity.underMaintenance()) {
        throw new Errors.EntityUnderMaintenanceError();
    }
}

export function getChildEntity(entity: BaseEntity, options?: FindEntityOptions): ChildEntity {
    const childEntity = entity.find(options) as ChildEntity;

    if (!childEntity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    return childEntity;
}

export function getChildIds(entity: BaseEntity): number[] {
    const asEntity = entity as Entity;

    let childIds = [];
    if (asEntity.child) {
        for (const child of asEntity.child) {
            childIds = [ ...childIds, child.id, ...getChildIds(child) ];
        }
    }
    return childIds;
}

export function getChildEntities(entity: BaseEntity): BaseEntity[] {
    const asEntity = entity as Entity;

    let childEntities: BaseEntity[] = [];
    if (asEntity.child) {
        for (const child of asEntity.child) {
            childEntities = [ ...childEntities, child, ...getChildEntities(child) ];
        }
    }
    return childEntities;
}

export function getParentIds(entity: BaseEntity): number[] {
    const asEntity = entity as Entity;
    const parent = asEntity.getParent && asEntity.getParent();

    if (!parent) {
        return [];
    }

    const ids = getParentIds(parent);
    if (ids) {
        return [ parent.id, ...ids ];
    }

    return [ parent.id ];
}

export function getParentEntities(entity: BaseEntity): BaseEntity[] {
    const asEntity = entity as Entity;
    const parent = asEntity.getParent && asEntity.getParent();

    if (!parent) {
        return [];
    }

    const parentEntities = getParentEntities(parent);
    if (parentEntities) {
        return [ parent, ...parentEntities ];
    }

    return [ parent ];
}

export function getBrands(entity: EntityInfo): EntityInfo[] {
    const asEntity = entity as Entity;
    if (asEntity.type === ENTITY_TYPE.BRAND || asEntity.type === ENTITY_TYPE.MERCHANT) {
        return [ entity ];
    }

    let brandIds = [];
    if (asEntity.child) {
        for (const child of asEntity.child) {
            brandIds = [ ...brandIds, ...getBrands(child) ];
        }
    }

    return brandIds;
}

export function getFlatEntities(entity: EntityInfo, first = true): EntityInfo[] {
    let flatEntities = [];
    if (first) {
        flatEntities.push(entity);
    }
    if (entity.child) {
        for (const child of entity.child) {
            flatEntities = [ ...flatEntities, child, ...getFlatEntities(child, false) ];
        }
    }
    return flatEntities;
}

export function getMerchantEntities(entity: EntityInfo): EntityInfo[] {
    if (entity.isMerchant) {
        return [ entity ];
    }

    let merchants = [];
    if (entity.child) {
        for (const child of entity.child) {
            merchants = [ ...merchants, ...getMerchantEntities(child) ];
        }
    }

    return merchants;
}

export function validateSuspended(entity: BaseEntity): void {
    if (entity.isSuspended()) {
        throw new Errors.ParentSuspendedError();
    }
}

export function isWebSiteWhitelistedCheckAvailable(brand: BaseEntity): boolean {
    const checkWebsiteWhitelisted = brand.inheritedWebSiteWhitelistedCheck;
    return checkWebsiteWhitelisted !== WEBSITE_WHITELISTED_CHECK_LEVEL.NONE && checkWebsiteWhitelisted !== null;
}

export async function updateWebSiteWhitelistedCheck(entity: BaseEntity,
                                                    value: WEBSITE_WHITELISTED_CHECK_LEVEL | null): Promise<void> {

    if (value === null || WEBSITE_WHITELISTED_CHECK_LEVEL[value.toUpperCase()]) {
        entity.checkWebSiteWhitelisted = value;
        await entity.save().finally(() => EntityCache.reset());
        return;
    }

    return Promise.reject(
        new Errors.ValidationError("Invalid value for web-site whitelisted check"));
}

export async function resetWebSiteWhitelistedCheck(entity: BaseEntity): Promise<void> {
    entity.checkWebSiteWhitelisted = null;
    await entity.save().finally(() => EntityCache.reset());
}

/**
 *  Gets short info of each parent up to master entity.
 */
export function getShortInfoOfParents(entity: BaseEntity, entityShortInfo?: EntityShortInfo): EntityShortInfo {
    const shortInfo = entity.structureToShortInfo({ basePath: ":" });
    if (entityShortInfo) {
        shortInfo.child = [ entityShortInfo ];
    }
    const asEntity = entity as Entity;
    if (!(asEntity.getParent && asEntity.getParent())) {
        return shortInfo;
    }
    return getShortInfoOfParents(asEntity.getParent(), shortInfo);
}

export async function findEntitiesByDomain(baseEntity: BaseEntity, domain: string): Promise<EntityWitPathInfo[]> {
    const sites = await getAvailableSiteService(baseEntity).getSitesByDomain(domain);
    const results: EntityWitPathInfo[] = [];
    for (const site of sites) {
        const entity = baseEntity.find({ id: site.entityId });
        if (entity) {
            results.push({
                id: entity.id,
                fullPath: makeRelativePath(entity, baseEntity.path)
            });
        }
    }

    if (!results.length) {
        throw new Errors.EntityCouldNotBeFound();
    }

    return results;
}

export async function findEntityByKeys(baseEntity: BaseEntity, keys: EntityKeyPath): Promise<EntityWitPathInfo> {
    const result = await searchEntityByKeys(baseEntity, keys);
    if (!result) {
        throw new Errors.EntityCouldNotBeFound();
    }

    return {
        id: baseEntity.id,
        fullPath: makeRelativePath(result, baseEntity.path)
    };
}

async function searchEntityByKeys(baseEntity: BaseEntity, keys: EntityKeyPath): Promise<BaseEntity> {
    const next = baseEntity.find({ key: keys.key });
    if (next && keys.child) {
        return searchEntityByKeys(next, keys.child);
    } else {
        return next;
    }
}

export function addDecryptedBrand(entityInfo: any) {
    if (entityInfo.type === ENTITY_TYPE.BRAND || entityInfo.type === ENTITY_TYPE.MERCHANT) {
        entityInfo.decryptedBrand = entityInfo.id;
    }
    if (entityInfo?.child) {
        entityInfo.child = entityInfo.child.map(item => addDecryptedBrand(item));
    }
    return entityInfo;
}

export function getMerchantEntitiesByPartOfCode(entity: EntityInfo & { merchant: MerchantInfo },
                                                code: string = ""): MerchantEntityInfo[] {
    const entities = getMerchantEntities(entity) as MerchantEntityInfo[];

    return entities.filter(({ merchant }) =>
        merchant && merchant.code.toString().toLowerCase().includes(code.toString().toLowerCase()));
}

export async function getBrandsWithChildren(entity: BaseEntity): Promise<BrandEntity[]> {
    let brands: BrandEntity[] = [];
    if ((entity as BrandEntity).isMerchant) {
        const merchant = await getMerchantCRUDService().findOne(entity as BrandEntity);
        if (merchant && !merchant.params.isPromoInternal) {
            return Promise.reject(new Errors.MerchantDoesntSupportError());
        }
        brands = [ entity as BrandEntity ];
    } else if (entity.isBrand()) {
        brands = [ entity as BrandEntity ];
    } else {
        brands = await findChildrenBrands(entity);
    }
    return brands;
}

export async function findChildrenBrands(entity: BaseEntity): Promise<BrandEntity[]> {

    let result: BrandEntity[] = [];
    if (entity.isBrand()) {
        result = result.concat([ entity as BrandEntity ]);
    } else if ((entity as BrandEntity).isMerchant) {
        const merchant = await getMerchantCRUDService().findOne(entity as BrandEntity);
        if (merchant && merchant.params.isPromoInternal) {
            result = result.concat([ entity as BrandEntity ]);
        }
    }
    const asParent = entity as Entity;
    const children = asParent.child;
    if (children) {
        for (const child of children) {
            const childBrands = await findChildrenBrands(child);
            result = result.concat(childBrands);
        }
    }
    return result;
}

export async function addMerchantCodes(structureInfo: EntityInfo): Promise<void> {
    if (structureInfo.isMerchant) {
        try {
            const merchant = await MerchantCache.findOne(structureInfo as BrandEntity);
            structureInfo.merchantCode = merchant.code;
        } catch (e) {
            log.warn(e, "Cannot find merchant for entity" + structureInfo.path);
        }
    }
    if (structureInfo.child) {
        for (const c of structureInfo.child) {
            await addMerchantCodes(c);
        }
    }
}
