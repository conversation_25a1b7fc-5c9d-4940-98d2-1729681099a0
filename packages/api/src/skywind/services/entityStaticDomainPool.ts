import { BaseEntity, ChildEntity } from "../entities/entity";
import * as Errors from "../errors";
import { getStaticDomainPoolService } from "./staticDomainPool";
import {
    ExtendedStaticDomain,
    StaticDomainPoolAttributes
} from "../entities/domainPool";

function pickRandom<T>(items: T[]): T {
    const randomIndex = Math.floor(Math.random() * items.length);
    return items[randomIndex];
}

export class EntityStaticDomainPoolService {
    constructor(
        private readonly entity: BaseEntity,
        private readonly domainPoolService = getStaticDomainPoolService()
    ) {
    }

    public async getPool(inherited: boolean = false): Promise<StaticDomainPoolAttributes> {
        if (inherited) {
            const pool = await this.findPool();
            if (!pool) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            return pool;
        } else {
            if (!this.entity.staticDomainPoolId) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            return this.domainPoolService.findById(this.entity.staticDomainPoolId);
        }
    }

    public async addPool(poolId: number): Promise<StaticDomainPoolAttributes> {
        const staticDomainPool = await this.domainPoolService.findById(poolId);
        this.entity.staticDomainPoolId = poolId;
        await this.entity.save();
        return staticDomainPool;
    }

    public async removePool(): Promise<void> {
        this.entity.staticDomainPoolId = null;
        await this.entity.save();
    }

    public async pickStaticDomain(): Promise<ExtendedStaticDomain | undefined> {
        const pool = await this.findPool();
        const domains = pool?.domains;
        if (!domains?.length) {
            return undefined;
        }
        return pickRandom(domains.filter(({ isActive }) => isActive));
    }

    public async pickLobbyDomain(): Promise<string | undefined> {
        const pool = await this.findPool();
        const domains = pool?.lobbyDomains;
        if (!domains?.length) {
            return undefined;
        }
        return pickRandom(domains.filter(({ isActive }) => isActive))?.name;
    }

    private async findPool(): Promise<StaticDomainPoolAttributes | undefined> {
        let childEntity = this.entity;
        while (!childEntity.isMaster() && !childEntity.staticDomainPoolId) {
            childEntity = (childEntity as ChildEntity).getParent();
        }
        if (!childEntity.staticDomainPoolId) {
            return undefined;
        }
        try {
            const pool = await this.domainPoolService.findById(childEntity.staticDomainPoolId);
            return {
                ...pool,
                ...(childEntity.path !== this.entity.path ? { inherited: true } : {})
            };
        } catch (err) {
            if (err instanceof Errors.DomainPoolNotFoundError) {
                return undefined;
            }
            throw err;
        }
    }
}
