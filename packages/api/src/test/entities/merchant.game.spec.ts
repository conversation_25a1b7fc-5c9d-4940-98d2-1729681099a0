import { expect, should, use } from "chai";
import * as Errors from "../../skywind/errors";
import {
    BY_IP,
    CN_IP,
    complexStructure,
    createComplexStructure,
    createRandomGameProvider,
    registerGame,
    resetDynamicDomain,
    setDynamicDomain,
    truncate,
    US_IP,
    withTransaction
} from "./helper";
import { BrandEntity } from "../../skywind/entities/brand";
import { Merchant } from "../../skywind/entities/merchant";
import { AnyMerchantAdapter } from "@skywind-group/sw-management-adapters";
import { BaseEntity, ChildEntity } from "../../skywind/entities/entity";
import * as EntityService from "../../skywind/services/entity";
import * as GameService from "../../skywind/services/game";
import * as jwt from "jsonwebtoken";
import EntitySettingsService from "../../skywind/services/settings";
import EntityCountryService from "../../skywind/services/entityCountry";
import getEntityFactory from "../../skywind/services/entityFactory";
import { getDynamicDomainService, getStaticDomainService } from "../../skywind/services/domain";
import { getEntityStaticDomainService } from "../../skywind/services/entityStaticDomainService";
import { ENTITY_GAME_STATUS } from "../../skywind/utils/common";
import { MerchantGameURLInfo, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { update } from "../../skywind/services/gameprovider";
import { SinonStub, stub } from "sinon";
import * as request from "request";
import { FACTORY } from "../factories/common";
import { getMerchantCRUDService, getMerchantService } from "../../skywind/services/merchant";
import { register } from "../helper";
import { getEntityGameService } from "../../skywind/services/entityGameService";
import config from "../../skywind/config";
import { createHash } from "../../skywind/utils/hash";
import { verifyStartGameToken } from "../../skywind/utils/token";

const FactoryGirl = require("factory-girl");
const factory = FactoryGirl.factory;

const chaiAsPromise = require("chai-as-promised");
should();
use(chaiAsPromise);

let country: string;
const TestAdapter: AnyMerchantAdapter = {

    createGameUrl(merchant: Merchant,
                  gameCode: string,
                  providerCode: string,
                  providerGameCode: string,
                  initRequest: any): Promise<MerchantGameURLInfo> {
        if (initRequest.playmode === "fun") {
            return Promise.resolve({
                tokenData: undefined,
                urlParams: {
                    playmode: PlayMode.FUN
                },
            });
        } else {
            return Promise.resolve({
                tokenData: {
                    playerCode: "SOME_PLAYER",
                    gameCode: gameCode,
                    providerCode,
                    providerGameCode,
                    brandId: merchant.brandId,
                    merchantSessionId: "SOME_SESSION_ID",
                    test: true,
                    country: country
                } as any,
                urlParams: {
                    playmode: PlayMode.REAL,
                    lobbyUrl: "http://lobby.com"
                },
            });
        }
    },
} as any;

describe("Gets merchant game url", () => {
    let provider;
    let brand: BrandEntity;
    let master: BaseEntity;

    before(async () => {
        await truncate();
        master = await createComplexStructure();

        await (new EntityCountryService(master.find({ key: complexStructure.tle1.key }) as ChildEntity))
            .add(["US", "CN"]);
        await (new EntityCountryService(master.find({ key: complexStructure.tle1ent1.key }) as ChildEntity))
            .add(["US", "CN"]);

        brand = await getEntityFactory(master.find({ path: ":TLE1:ENT1:" })).createBrand({
            name: "brand1",
            description: "brand1 description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-1.com"
        });
        brand.addCountry("CN");
        brand.addCountry("US");
        await brand.save();

        await register("test", TestAdapter);
        await getMerchantCRUDService().create(brand, {
            type: "test",
            code: "merch13",
            params: {
                param1: "p1",
                param2: "p2",
            },
        });

        provider = await createRandomGameProvider();
        await registerGame(provider.code, "GAME001", "Game 1");
        const byGameCode = "GAME002-Belarus";
        await registerGame(provider.code, byGameCode, "Game BY");
        await update(byGameCode, { countries: ["BY", "CN"] } as any);

        const gameCode2 = "GAME00222";
        await registerGame(provider.code, gameCode2, "Game BY2");

        const gameCode3 = "GAME00333";
        await registerGame(provider.code, gameCode3, "Game BY3");

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, "GAME001", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, "GAME001", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, "GAME001",
            false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, byGameCode, false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, byGameCode, false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, byGameCode,
            false, { status: "normal" });

        await GameService.addGameToEntity(
            master,
            { path: ":TLE1:" },
            gameCode2,
            false,
            { status: "normal", settings: { countries: ["BY", "CN"] } }
        );
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, gameCode2, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, gameCode2, false);

        await GameService.addGameToEntity(
            master,
            { path: ":TLE1:" },
            gameCode3,
            false,
            { status: "normal", settings: { countries: ["!US", "!CN"] } }
        );
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, gameCode3, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, gameCode3, false);
    });

    beforeEach(async () => {
        country = "US";
        config.merchantGameRestrictionsUseIp = false;
    });

    it("Gets merchant game url without playmode", async () => {
        const result = await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            country: "null"
        } as any, { ip: US_IP });

        expect(jwt.decode(result.token)).contains({
            "brandId": brand.id,
            "gameCode": "GAME001",
            "merchantSessionId": "SOME_SESSION_ID",
            "playerCode": "SOME_PLAYER",
            "providerCode": provider.code,
            "providerGameCode": "GAME001",
            "playmode": "real",
            "country": "US"
        });
        expect(result.url).to.equal("http://url.test/?language=en&playmode=real&lobbyUrl=http%3A%2F%2Flobby.com");
    });

    it("Gets merchant game url", async () => {
        const result = await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL,
            language: "en",
            lobby: "http://google.com"
        }, { ip: US_IP });

        expect(jwt.decode(result.token)).contains({
            "brandId": brand.id,
            "gameCode": "GAME001",
            "merchantSessionId": "SOME_SESSION_ID",
            "playerCode": "SOME_PLAYER",
            "providerCode": provider.code,
            "providerGameCode": "GAME001",
        });

        expect(result.url).to.equal("http://url.test/?language=en&lobby=http%3A%2F%2Fgoogle.com&playmode=real&lobbyUrl=http%3A%2F%2Flobby.com");
    });

    it("Gets merchant game url in fun mode", async () => {
        const result = await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.FUN,
        }, { ip: US_IP });

        expect(result.token).not.exist;
        expect(result.url).to.equal("http://url.test/?language=en&playmode=fun");
    });

    it("Fails to get merchant game url when game not found", async () => {
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "provider1GAME002",
            playmode: PlayMode.REAL
        }, { ip: US_IP }).should.eventually.rejectedWith(Errors.GameNotFoundError);
    });

    it("Fails to get merchant game url when brand is suspended", withTransaction(async () => {
        await EntityService.suspend(master, { id: brand.id });
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL
        }, { ip: US_IP }).should.eventually.rejectedWith(Errors.ParentSuspendedError);
    }));

    it("Fails to get merchant game url when entity game is hidden", withTransaction(async () => {
        await getEntityGameService(brand).update("GAME001", { status: ENTITY_GAME_STATUS.HIDDEN });
        try {
            await getMerchantService().getGameUrl({
                merchantType: "test",
                merchantCode: "merch13",
                gameCode: "GAME001",
                playmode: PlayMode.REAL
            }, { ip: US_IP }).should.eventually.rejectedWith(Errors.GameSuspendedError);
        } finally {
            await getEntityGameService(brand).update("GAME001", { status: ENTITY_GAME_STATUS.NORMAL });
        }
    }));

    it("Fails to get merchant game url when merchant not found", async () => {
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch_1",
            gameCode: "provider1GAME002",
            playmode: PlayMode.REAL
        }, { ip: US_IP }).should.eventually.rejectedWith(Errors.MerchantNotFoundError);
    });

    it("Gets merchant game url with playmode, lang and currency", async () => {
        await registerGame(provider.code, "GAME002", "Game 1",
            "http://url.test?lang={lang}&currency={currency}&playmode={playmode}");
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, "GAME002", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, "GAME002", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, "GAME002",
            false, { status: "normal" });
        const result = await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME002",
            playmode: PlayMode.REAL,
            lobby: "http://lobby.com"
        }, { ip: US_IP });

        expect(result.url).to.eq("http://url.test/?lang=en&currency=USD&playmode=real&language=en&" +
            "lobby=http%3A%2F%2Flobby.com&lobbyUrl=http%3A%2F%2Flobby.com");
    });

    describe("Gets merchant game url with merchant domain", () => {

        before(async () => {
            const dynamicDomain = await getDynamicDomainService().create({
                domain: "gameserver.skywindgroup.com",
                environment: "test"
            });
            await setDynamicDomain(brand, dynamicDomain);
            const staticDomain = await getStaticDomainService().create({
                domain: "game3.skywindgroup.com",
            });
            await getEntityStaticDomainService().set(brand, staticDomain.id);
        });

        after(async () => {
            await resetDynamicDomain(brand);
            await getEntityStaticDomainService().reset(brand);
        });

        it("Gets merchant game url with merchant domain", async () => {
            await registerGame(provider.code, "GAME003", "Game 1",
                "http://{staticDomain}/game3?url=http://{dynamicDomain}");
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, "GAME003", false, { status: "normal" });
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, "GAME003", false, { status: "normal" });
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, "GAME003",
                false, { status: "normal" });
            const result = await getMerchantService().getGameUrl({
                merchantType: "test",
                merchantCode: "merch13",
                gameCode: "GAME003",
                playmode: PlayMode.REAL,
                cashier: "http://lobby.com"
            }, { ip: US_IP });

            expect(result.url).to.eq("http://game3.skywindgroup.com/game3?" +
                "url=http%3A%2F%2Fgameserver.skywindgroup.com&" +
                "language=en&cashier=http%3A%2F%2Flobby.com&playmode=real&lobbyUrl=http%3A%2F%2Flobby.com");
        });

        it("Gets merchant game url with parent entityGame domain", async () => {
            await registerGame(provider.code, "GAME783", "Game 4",
                "http://{staticDomain}/game3?url=http://{dynamicDomain}");
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, "GAME783", false,
                { status: "normal" });
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, "GAME783", false,
                { status: "normal", domain: "game783.skywindgroup.com" });
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, "GAME783",
                false, { status: "normal" });
            const result = await getMerchantService().getGameUrl({
                merchantType: "test",
                merchantCode: "merch13",
                gameCode: "GAME783",
                playmode: PlayMode.REAL,
                cashier: "http://lobby.com"
            }, { ip: US_IP });

            expect(result.url).to.eq("http://game783.skywindgroup.com/game3?" +
                "url=http%3A%2F%2Fgameserver.skywindgroup.com&" +
                "language=en&cashier=http%3A%2F%2Flobby.com&playmode=real&lobbyUrl=http%3A%2F%2Flobby.com");
        });

    });

    it("Fails to get merchant game url without static domain", async () => {
        await registerGame(provider.code, "GAME004", "Game 1",
            "http://{staticDomain}/game4");
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, "GAME004", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, "GAME004", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, "GAME004",
            false, { status: "normal" });
        await expect(getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME004",
            playmode: PlayMode.REAL
        }, { ip: US_IP })).to.be.rejectedWith(Errors.StaticDomainNotDefined);
    });

    it("Fails to get merchant game url without dynamic domain", async () => {
        await registerGame(provider.code, "GAME005", "Game 1",
            "http://game.skywindgroup.com/game5?url=http://{dynamicDomain}");
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, "GAME005", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, "GAME005", false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:brand1:" }, "GAME005",
            false, { status: "normal" });
        await expect(getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME005",
            playmode: PlayMode.REAL
        }, { ip: US_IP })).to.be.rejectedWith(Errors.DynamicDomainNotDefined);
    });

    it("Gets merchant game url with operator country", async () => {
        const result = await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL
        }, {});

        expect(jwt.decode(result.token)).contains({
            "operatorCountry": "US"
        });
    });

    it("Gets merchant game url with wrong operator country", async () => {
        country = "GBR";
        const result = await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL
        }, {});

        const data = jwt.decode(result.token) as any;
        expect(data.operatorCountry).to.be.undefined;
    });

    it("Fails to get merchant game url when ip not resolved", async () => {
        config.merchantGameRestrictionsUseIp = true;
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL
        }, { ip: "unknown" }).should.eventually.rejectedWith(Errors.UnknownIpAddress);
    });

    it("Fails to get merchant game url when country is restricted", async () => {
        country = "BY";
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL
        }, { ip: BY_IP }).should.eventually.rejectedWith(Errors.CountryIsRestricted);
    });

    it("Fails to get merchant game url when currency is restricted", async () => {
        country = "CN";
        const service = new EntitySettingsService(brand);
        await service.patch({
            restrictions: {
                countries: {
                    "CN": ["CNY"]
                }
            }
        });
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME001",
            playmode: PlayMode.REAL
        }, { ip: CN_IP }).should.eventually.rejectedWith(Errors.CurrencyIsRestricted);
    });

    it("Fails to get merchant game url when country in game is restricted", async () => {
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME002-Belarus",
            playmode: PlayMode.REAL
        }, { ip: US_IP }).should.eventually.rejectedWith(Errors.CountryIsRestricted);
    });

    it("Fails to get merchant game url when country in entity-game is restricted[whitelist]", async () => {
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME00222",
            playmode: PlayMode.REAL
        }, { ip: US_IP }).should.eventually.rejectedWith(Errors.CountryIsRestricted);
    });

    // tslint:disable-next-line:max-line-length
    it("Fails to get merchant game url when country in entity-game is restricted[blacklist] on parent entity game level", async () => {
        await getMerchantService().getGameUrl({
            merchantType: "test",
            merchantCode: "merch13",
            gameCode: "GAME00333",
            playmode: PlayMode.REAL
        }, { ip: US_IP }).should.eventually.rejectedWith(Errors.CountryIsRestricted);
    });

    describe("Gets merchant game url and applies custom logic", () => {

        it("Gets merchant game url, applies cashier=0 param to url overwriting cashier from query", async () => {
            const service = new EntitySettingsService(brand);
            await service.patch({
                urlParams: {
                    cashier: 0,
                    lobby: 0
                }
            } as any);

            const result = await getMerchantService().getGameUrl({
                merchantType: "test",
                merchantCode: "merch13",
                gameCode: "GAME001",
                playmode: PlayMode.REAL,
                language: "en",
                cashier: "x",
                lobby: "y"
            }, { ip: US_IP });

            expect(jwt.decode(result.token)).contains({
                "brandId": brand.id,
                "gameCode": "GAME001",
                "merchantSessionId": "SOME_SESSION_ID",
                "playerCode": "SOME_PLAYER",
                "providerCode": provider.code,
                "providerGameCode": "GAME001",
            });
            expect(result.url)
                .to
                .equal("http://url.test/?cashier=0&language=en&playmode=real");
        });

        it("Gets merchant game url, ensure cashier=0 param in url when there is cashier param in merchant response",
            async () => {
                TestAdapter.createGameUrl = (merchant: Merchant,
                                             gameCode: string,
                                             providerCode: string,
                                             providerGameCode: string,
                                             initRequest: any) => {
                    return Promise.resolve({
                        tokenData: {
                            playerCode: "SOME_PLAYER",
                            gameCode: gameCode,
                            providerCode,
                            providerGameCode,
                            brandId: merchant.brandId,
                            merchantSessionId: "SOME_SESSION_ID",
                            test: true
                        } as any,
                        urlParams: {
                            playmode: PlayMode.REAL,
                            lobbyUrl: "http://lobby.com",
                            cashier: "y"
                        },
                    });
                };

                const service = new EntitySettingsService(brand);
                await service.update({
                    urlParams: {
                        cashier: 0
                    }
                } as any);

                const result = await getMerchantService().getGameUrl({
                    merchantType: "test",
                    merchantCode: "merch13",
                    gameCode: "GAME001",
                    playmode: PlayMode.REAL,
                    language: "en"
                }, { ip: US_IP });

                expect(jwt.decode(result.token)).contains({
                    "brandId": brand.id,
                    "gameCode": "GAME001",
                    "merchantSessionId": "SOME_SESSION_ID",
                    "playerCode": "SOME_PLAYER",
                    "providerCode": provider.code,
                    "providerGameCode": "GAME001",
                });
                expect(result.url)
                    .to
                    .equal("http://url.test/?cashier=0&language=en&playmode=real&lobbyUrl=http%3A%2F%2Flobby.com");
            });

        it("Gets merchant game url hashes of lobby url", async () => {
            await new EntitySettingsService(brand).patch({
                hashLobbyAndCashierEnabled: true,
                urlParams: {
                    lobby: "1"
                }
            });

            const backUrl = "https://gamelauncher-uu-pop.sunbingo.co.uk/dataCenterId/frpg/static/uu-**********/fakepager?page=https%3A%2F%2Fwww.sunbingo.co.uk";
            TestAdapter.createGameUrl = (merchant: Merchant,
                                         gameCode: string,
                                         providerCode: string,
                                         providerGameCode: string,
                                         initRequest: any) => {
                return Promise.resolve({
                    tokenData: {
                        playerCode: "SOME_PLAYER",
                        gameCode: gameCode,
                        providerCode,
                        providerGameCode,
                        brandId: merchant.brandId,
                        merchantSessionId: "SOME_SESSION_ID",
                        test: true
                    } as any,
                    urlParams: {
                        playmode: PlayMode.REAL,
                        lobby: encodeURIComponent(backUrl)
                    },
                });
            };

            const result = await getMerchantService().getGameUrl({
                merchantType: "test",
                merchantCode: "merch13",
                gameCode: "GAME001",
                playmode: PlayMode.REAL,
                language: "en"
            }, { ip: US_IP });

            expect(result.token).exist;
            const tokenData = await verifyStartGameToken(result.token);
            expect(tokenData.lb).to.eq(createHash(backUrl));

            const url = new URL(result.url);
            expect(Object.fromEntries(url.searchParams.entries())).deep.equal({
                cashier: "0",
                lobby: backUrl,
                language: "en",
                playmode: "real"
            });
        });
    });

    describe("Internal POP", () => {
        let postRequestToPOP: SinonStub;
        let merchantEntity: BaseEntity;

        before(async () => {
            postRequestToPOP = stub(request, "post");
            merchantEntity = await factory.create(FACTORY.MERCHANT_ENTITY, {}, { parent: master });
            await factory.create(FACTORY.MERCHANT, {}, {
                entityId: merchantEntity.id,
                brandId: merchantEntity.id,
                type: "pop",
                code: "123pop_merchant123",
                params: {
                    serverUrl: "http://merchant.url",
                    password: "Qwertyu123456"
                }
            });

            await GameService.addGameToEntity(master, { id: merchantEntity.id },
                "GAME001", false, { status: "normal" });

            postRequestToPOP.yields(null, { statusCode: 200 }, {
                accountBalance: {
                    currencyCode: "USD",
                    playerId: "123",
                    accountId: 2
                },
                secureToken: "token_abcdddd",
                additionalInfo: {
                    isInternalPlayer: "1"
                }
            });
        });

        after(async () => {
            postRequestToPOP.resetBehavior();
            postRequestToPOP.restore();
        });

        it("Gets POP merchant game url in real mode", async () => {
            let result = await getMerchantService().getGameUrl({
                merchantType: "pop",
                merchantCode: "123pop_merchant123",
                gameCode: "GAME001",
                playmode: PlayMode.REAL,
                cashier: "http://localhost.com",
                ticket: "123456",
                username: "qwerty"
            } as any, { ip: US_IP });

            expect(result.token).exist;
            expect(result.url).to.equal("http://url.test/?language=en&cashier" +
                `=${encodeURIComponent("http://localhost.com")}&playmode=real`);

            result = await getMerchantService().getGameUrl({
                merchantType: "pop",
                merchantCode: "123pop_merchant123",
                gameCode: "GAME001",
                playmode: PlayMode.REAL,
                cashier: encodeURIComponent("http://localhost.com"),
                ticket: "123456",
                username: "qwerty"
            } as any, { ip: US_IP });

            expect(result.token).exist;

            // double encoded is fixed
            expect(result.url).to.equal("http://url.test/?language=en&cashier" +
                `=${encodeURIComponent("http://localhost.com")}&playmode=real`);

            const url = new URL(result.url);
            expect(url.origin).to.equal("http://url.test");
            expect(Object.fromEntries(url.searchParams.entries())).deep.equal({
                cashier: "http://localhost.com",
                language: "en",
                playmode: "real"
            });
        });

        it("Gets POP merchant game url with url params in settings in real mode", async () => {
            await new EntitySettingsService(merchantEntity).patch({
                hashLobbyAndCashierEnabled: true,
                urlParams: {
                    cashier: 0
                } as any
            });

            postRequestToPOP.yields(null, { statusCode: 200 }, {
                accountBalance: {
                    currencyCode: "USD",
                    playerId: "123",
                    accountId: 2
                },
                secureToken: "token_abcdddd",
                urls: {
                    lobby: "http://poplobby-should-have-more-priority.com",
                    cashier: "http://value-is-not-used.com"
                }
            });

            const backUrl = "https://gamelauncher-uu-pop.sunbingo.co.uk/dataCenterId/frpg/static/uu-**********/fakepager?page=https%3A%2F%2Fwww.sunbingo.co.uk";

            const result = await getMerchantService().getGameUrl({
                merchantType: "pop",
                merchantCode: "123pop_merchant123",
                gameCode: "GAME001",
                playmode: PlayMode.REAL,
                cashier: "http://localhost.com",
                ticket: "123456",
                username: "qwerty",
                backurl: backUrl
            } as any, { ip: US_IP });

            expect(result.token).exist;
            const tokenData = await verifyStartGameToken(result.token);
            expect(tokenData.lb).to.eq(createHash(backUrl));

            const url = new URL(result.url);
            expect(url.origin).to.equal("http://url.test");
            expect(Object.fromEntries(url.searchParams.entries())).deep.equal({
                cashier: "0",
                lobby: backUrl,
                language: "en",
                playmode: "real",
                merch_login_url: backUrl
            });
        });
    });
});
