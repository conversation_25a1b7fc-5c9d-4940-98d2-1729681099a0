import { expect } from "chai";
import * as sinon from "sinon";
import { factory } from "factory-girl";
import { publicId } from "@skywind-group/sw-utils";
import { MerchantGameInitRequest, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { EntityGame, Game, PlayerGameURLInfo } from "../../../skywind/entities/game";
import { FACTORY } from "../../factories/common";
import { getGameURLInfo } from "../../../skywind/services/gameUrl/getGameURLInfo";
import { createPlayerSessionFacade } from "../../../skywind/services/player/playerSessionFacade";
import { EmailTemplate, EntitySettings } from "../../../skywind/entities/settings";
import { truncate } from "../../entities/helper";
import * as settingsService from "../../../skywind/services/settings";
import * as entityDynamicDomainService from "../../../skywind/services/entityDynamicDomainService";
import { getBrandPlayerValidator } from "../../../skywind/services/brandPlayerValidator";
import { verifyStartGameToken } from "../../../skywind/utils/token";
import { EntityHelper } from "../../../skywind/services/gameUrl/entityHelper";
import * as entityJrsdService from "../../../skywind/services/entityJurisdiction";
import config from "../../../skywind/config";
import { Lobby } from "../../../skywind/entities/lobby";
import { Player } from "../../../skywind/entities/player";
import { BrandEntity } from "../../../skywind/entities/brand";

const url = require("url");

describe("GetGameURLInfo_DirectLaunch", async () => {
    const emailTemplate: EmailTemplate = {
        from: "",
        subject: "",
        html: ""
    };
    const entitySettings: EntitySettings = {
        emailTemplates: {
            passwordRecovery: emailTemplate,
            changeEmail: emailTemplate
        },
        urlParams: {
            sound_popup: "true"
        },
        gameSplashes: {
            sw_csgo: "sw_pt",
            sw_pubg: "sw_pg"
        },
        launchGameInsideLobby: true
    };
    const dynamicEntityDomainServiceFake = {
        get() {
            return {
                domain: "http://localhost:4000"
            };
        }
    };
    const staticEntityDomainServiceFake = {
        get() {
            return undefined;
        }
    };
    const entityJrsdServiceFake = {
        findOne() {
            return undefined;
        },
        findAll() {
            return [];
        }
    };

    const depGame = {
        id: 1,
        code: "GAME001",
        url: "http://gc.gaming.skywindgroup.com/mrmonkey/{clientVersion}/index.html" +
            "?startGameToken={startGameToken}&language={lang}",
        gameProvider: {
            code: "GamePro"
        },
        providerGameCode: "ProGame",
        status: "normal",
        defaultClientVersion: "1.2.3",
        isLiveGame: () => true
    } as Game;

    const entityGame = {
        id: 1,
        entityId: 1,
        gameId: 1,
        parentEntityGameId: 1,
        game: depGame,
        isLiveGame: () => true
    } as EntityGame;

    const language = "en";

    const payload = {
        playMode: PlayMode.REAL,
        language: "en"
    };

    const merchantRequest = {
        ...payload,
        merchantType: "someType",
        merchantCode: "MERCHANT001",
        gameCode: "GAME001",
        ticket: "TICKET"
    } as MerchantGameInitRequest;

    const merchantSessionId = "merchant_session_id";
    let brand: BrandEntity;
    let lobby: Lobby;
    let player: Player;

    let getEntitySettingsStub: sinon.SinonStub;
    let getEntityDynamicDomainServiceStub: sinon.SinonStub;
    let validateBonusCoinsAvailableStub: sinon.SinonStub;
    let getAvailableLanguageStub: sinon.SinonStub;
    let getEntityJurisdictionServiceStub: sinon.SinonStub;
    let findAllStub: sinon.SinonStub;

    before(async () => {
        await truncate();

        brand = await factory.create(FACTORY.BRAND, {}, {
            currencies: ["CNY", "USD", "EUR", "RUB"],
            countries: ["US", "RU", "DE", "IT"]
        });
        lobby = await factory.create(FACTORY.LOBBY, {}, { isDefault: true, brandId: brand.id });
        player = await factory.create(FACTORY.PLAYER, { brandId: brand.id });

        getEntitySettingsStub = sinon.stub(settingsService, "getEntitySettings");
        getEntityDynamicDomainServiceStub = sinon.stub(entityDynamicDomainService, "getEntityDynamicDomainService");
        validateBonusCoinsAvailableStub = sinon.stub(getBrandPlayerValidator(), "validateBonusCoinsAvailable");
        getAvailableLanguageStub = sinon.stub(EntityHelper, "getAvailableLanguage");
        getEntityJurisdictionServiceStub = sinon.stub(entityJrsdService, "getEntityJurisdictionService");
        findAllStub = sinon.stub(entityJrsdServiceFake, "findAll");
    });

    afterEach(async () => {
        getEntitySettingsStub.restore();
        getEntityDynamicDomainServiceStub.restore();
        validateBonusCoinsAvailableStub.restore();
        getAvailableLanguageStub.restore();
        getEntityJurisdictionServiceStub.restore();
        findAllStub.restore();
    });

    it("brand", async () => {
        getEntitySettingsStub.returns(entitySettings);
        getEntityDynamicDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
        getEntityDynamicDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
        validateBonusCoinsAvailableStub.returns(true);
        getAvailableLanguageStub.returns(language);
        getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);
        findAllStub.returns([]);

        await (createPlayerSessionFacade()).create({
            playerCode: player.code,
            brandId: brand.id,
            playerId: player.id,
            sessionId: "test"
        });

        const operatorLobby = "https://operator.com/lobby/url";
        const { url: actualUrl }: PlayerGameURLInfo = await getGameURLInfo({
            entityGame,
            brand,
            entitySettings,
            disableLauncher: true,
            merchant: null,
            player,
            isLobby: false,
            request: { ...payload, lobby: operatorLobby }
        });

        const parsedUrl = url.parse(actualUrl, false);
        expect(parsedUrl.host)
            .to.be.equal(`${publicId.instance.encode(lobby.id).toLowerCase()}-${config.lobbies.domainTemplate}`);
        expect(parsedUrl.hash).to.be.contain(`#/direct-launch/${entityGame.game.code}`);

        // query could be parsed only if params are placed between '?' and '#'
        const backHashUrl = actualUrl.replace("#/", "") + "#";
        const query = url.parse(backHashUrl, true).query;
        expect(query.language).to.be.equal("en");
        expect(query.lobby).to.be.equal(operatorLobby);
    });

    it("merchant", async () => {
        const merchant = await factory.create(FACTORY.MERCHANT, { brandId: brand.id });
        const createGameUrlStub = sinon.stub(merchant, "createGameUrl");
        createGameUrlStub.returns(Promise.resolve({
            urlParams: {},
            tokenData: {
                country: "US",
                language: "en",
                providerCode: "PROVIDER001",
                providerGameCode: "PRGAME001",
                playerCode: "PLAYER001",
                gameCode: "GAME001",
                brandId: 3255,
                currency: "EUR",
                merchantType: "ipm",
                merchantCode: "MERCH001",
                sessionId: merchantSessionId
            }
        }));

        getEntitySettingsStub.returns(entitySettings);
        getEntityDynamicDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
        getEntityDynamicDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
        getAvailableLanguageStub.returns(language);
        getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

        const { url: actualUrl } = await getGameURLInfo({
            entityGame,
            brand,
            entitySettings,
            disableLauncher: true,
            merchant,
            isLobby: false,
            request: merchantRequest
        });

        const parsedUrl = url.parse(actualUrl, false);
        expect(parsedUrl.host)
            .to.be.equal(`${publicId.instance.encode(lobby.id).toLowerCase()}-${config.lobbies.domainTemplate}`);
        expect(parsedUrl.hash).to.be.contain(`#/direct-launch/${entityGame.game.code}`);

        // query could be parsed only if params are placed between '?' and '#'
        const backHashUrl = actualUrl.replace("#/", "") + "#";
        const lobbyQuery = url.parse(backHashUrl, true).query;
        expect(lobbyQuery.language).to.be.equal("en");

        const gameQuery = url.parse(lobbyQuery.url, true).query;

        const startGameTokenData = await verifyStartGameToken(gameQuery.startGameToken);
        expect(startGameTokenData.lobbySessionId).eq(merchantSessionId);
    });
});
