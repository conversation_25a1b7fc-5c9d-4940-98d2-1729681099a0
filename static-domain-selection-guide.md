# Static Domain Selection Guide for Games

This guide explains how static domains are selected for games in the Skywind Management API system and the rules that govern this selection process.

## Overview

Static domains are used to serve game content and are selected through a hierarchical system that considers multiple factors including play mode, game-specific configurations, entity settings, and domain pools.

## Domain Selection Flow

The static domain selection follows this priority order:

### 1. Fun Mode Domain (Highest Priority)
```typescript
if (playMode === PlayMode.FUN && this.entityGame.game.clientFeatures?.funModeDomain) {
    return this.entityGame.game.clientFeatures?.funModeDomain;
}
```

**Rule**: If the game is being played in FUN mode and the game has a specific `funModeDomain` configured in its `clientFeatures`, this domain takes absolute priority.

**Use Case**: Special domains for demo/fun play that might have different content or configurations.

### 2. Entity Game Specific Domain
```typescript
if (this.entityGame.domain) {
    return this.entityGame.domain;
}
```

**Rule**: If a specific domain is configured directly on the EntityGame record, it overrides all other domain selection logic except fun mode domains.

**Use Case**: Games that require specific domains due to licensing, regulatory, or technical requirements.

### 3. Static Domain Pool Selection
```typescript
const pickedStaticDomain = await this.entityStaticDomainPoolService.pickStaticDomain();
```

**Rule**: If no specific domain is configured, the system attempts to select a domain from the entity's static domain pool.

#### Pool Selection Algorithm:
1. **Find Pool**: Traverses the entity hierarchy to find a static domain pool
2. **Filter Active Domains**: Only considers domains marked as `isActive: true`
3. **Random Selection**: Uses `pickRandom()` to randomly select from active domains

```typescript
// Pool finding logic with inheritance
let childEntity = this.entity;
while (!childEntity.isMaster() && !childEntity.staticDomainPoolId) {
    childEntity = (childEntity as ChildEntity).getParent();
}
```

**Inheritance Rule**: If the current entity doesn't have a domain pool, the system traverses up the entity hierarchy until it finds one or reaches the master entity.

### 4. Fallback to Entity Static Domain Service
```typescript
const staticDomain = pickedStaticDomain || await getEntityStaticDomainService().get(this.brand);
```

**Rule**: If no domain is found in the pool, falls back to the entity's configured static domain.

#### Fallback Selection Algorithm:
```typescript
let parent = entity;
while (!parent.isMaster() && !parent.staticDomainId) {
    parent = (parent as ChildEntity).getParent();
}
```

**Inheritance Rule**: Similar to pool selection, traverses up the entity hierarchy to find a configured static domain.

## Domain Validation Rules

### 1. Static Domain Tags
```typescript
export function isStaticDomainEnabled(domain: string, staticDomainTags?: string[]): boolean {
    if (!staticDomainTags || !staticDomainTags.length) {
        return true;
    }
    const cleanedDomain = domain.toLowerCase();
    for (const domainTag of staticDomainTags) {
        if (cleanedDomain.includes(domainTag.toLowerCase())) {
            return true;
        }
    }
    return false;
}
```

**Rule**: If an entity has `staticDomainTags` configured, only domains containing those tags (case-insensitive substring match) are allowed.

**Example**: If `staticDomainTags = ["chebureck-egor.com"]`, only domains containing "chebureck-egor.com" will be valid.

### 2. Parent Entity Restrictions
```typescript
if (parentSettings.allowedStaticDomainsForChildId) {
    if (parentSettings.allowedStaticDomainsForChildId.indexOf(domain.id) === -1) {
        throw new Errors.ValidationError("Static domains restricted in parent entities");
    }
}
```

**Rule**: Parent entities can restrict which static domains their child entities can use through the `allowedStaticDomainsForChildId` setting.

## Domain Pool Management

### Pool Structure
- **Static Domain Pool**: Contains multiple static domains
- **Active Status**: Each domain in the pool has an `isActive` flag
- **Random Selection**: Active domains are selected randomly for load distribution

### Pool Inheritance
- Child entities inherit domain pools from parent entities
- The system traverses up the hierarchy until it finds a pool
- Pools are marked as `inherited: true` when inherited from parent

## Special Considerations

### 1. Lobby Domains
```typescript
public async pickLobbyDomain(): Promise<string | undefined> {
    const pool = await this.findPool();
    const domains = pool?.lobbyDomains;
    if (!domains?.length) {
        return undefined;
    }
    return pickRandom(domains.filter(({ isActive }) => isActive))?.name;
}
```

**Rule**: Lobby domains follow similar selection logic but are separate from static game domains.

### 2. Maintenance Mode
```typescript
if (this.brand.underMaintenance()) {
    return this.getMaintenanceUrl();
}
```

**Rule**: If the brand is under maintenance, a special maintenance URL is returned instead of normal domain selection.

### 3. Analytics Tracking
All domain selections are tracked for analytics purposes:
```typescript
const domainAnalyticsData: DomainAnalyticsData = {
    type: config.analytics.domains.type,
    ts: Date.now(),
    brandId: this.brand.id,
    gameCode: game.code,
    playerCode: startGameTokenData?.playerCode,
    initiator: "game-get-url",
    staticDomain: staticDomainHost,
    dynamicDomain: dynamicDomainHost,
    ehubDomain: this.extractDomain(queryParams.phantom_version_host as string)
};
```

## Configuration Examples

### 1. Game-Specific Fun Mode Domain
```json
{
  "clientFeatures": {
    "funModeDomain": "fun-games.example.com"
  }
}
```

### 2. Entity Game Domain Override
```json
{
  "domain": "special-game.example.com"
}
```

### 3. Static Domain Tags
```json
{
  "staticDomainTags": ["region1.com", "special-license.net"]
}
```

### 4. Parent Domain Restrictions
```json
{
  "allowedStaticDomainsForChildId": [1, 2, 5]
}
```

## Best Practices

1. **Use Domain Pools**: For load distribution and redundancy
2. **Configure Fun Mode Domains**: When demo content differs from real money games
3. **Set Domain Tags**: For regulatory or regional compliance
4. **Monitor Active Status**: Regularly review and update domain active status
5. **Test Inheritance**: Verify domain selection works correctly across entity hierarchy
6. **Track Analytics**: Use domain analytics to monitor performance and usage patterns

## Troubleshooting

### Common Issues:
1. **No Domain Selected**: Check entity hierarchy for domain pool or static domain configuration
2. **Domain Validation Errors**: Verify domain tags and parent restrictions
3. **Wrong Domain Selected**: Check priority order and entity-specific configurations
4. **Inactive Domains**: Ensure domains in pools are marked as active

### Debug Steps:
1. Check `entityGame.domain` for game-specific overrides
2. Verify `staticDomainPoolId` in entity hierarchy
3. Confirm domain pool contains active domains
4. Validate domain tags and parent restrictions
5. Review analytics data for domain selection patterns
